/*
 * @Description: 列表示例mock数据
 * @Author: XIAOLIJUN
 * @Date: 2022-07-07 19:41:02
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2022-07-12 14:18:33
 */
import { defHttp } from '/@/utils/http/axios';
import { DemoParams, DemoListGetResultModel } from './model/tableModel';

enum Api {
  DEMO_LIST = '/mock/table/getDemoList',
}

/**
 * @description: Get sample list value
 */

export const demoListApi = (params: DemoParams) =>
  defHttp.get<DemoListGetResultModel>({
    url: Api.DEMO_LIST,
    params,
    headers: {
      // @ts-ignore
      ignoreCancelToken: true,
    },
  });
