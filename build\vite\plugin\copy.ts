/*
 * @Description: 拷贝静态资源插件
 * @Author: XIAOLIJUN
 * @Date: 2023-02-02 17:52:32
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2023-02-03 14:03:18
 */
import createFilesCopyPlugin, { patternItem } from 'vite-plugin-files-copy';
import { OUTPUT_DIR } from '../../constant';

export function configFilesCopyPlugin(env: ViteEnv) {
  const { VITE_COPY_CESIUM } = env;

  const patterns: Array<patternItem> = [];

  const cesiumLib: patternItem = {
    from: 'node_modules/cesium/Build/Cesium',
    to: `${OUTPUT_DIR}/resource/lib/Cesium`,
  };
  VITE_COPY_CESIUM && patterns.push(cesiumLib);

  const filesCopyPlugin = createFilesCopyPlugin({ patterns });
  return filesCopyPlugin;
}
