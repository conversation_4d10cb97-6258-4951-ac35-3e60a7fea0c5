/*
 * @Description: 接口数据格式用于返回统一的格式
 * @Author: XIAOLIJUN
 * @Date: 2022-07-07 19:41:05
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2022-09-30 09:55:20
 */

import { ResultEnum } from '/@/enums/httpEnum';

export function resultSuccess<T = Recordable>(data: T, { message = '请求成功' } = {}) {
  return {
    code: ResultEnum.SUCCESS,
    data,
    message,
    success: true,
  };
}

export function resultPageSuccess<T = any>(
  page: number,
  pageSize: number,
  list: T[],
  { message = '请求成功' } = {},
) {
  const pageData = pagination(page, pageSize, list);

  return {
    ...resultSuccess({
      rows: pageData,
      totalRows: list.length,
    }),
    message,
  };
}

export function resultError(message = '请求失败', { code = ResultEnum.ERROR, data = null } = {}) {
  return {
    code,
    data,
    message,
    success: false,
  };
}

export function pagination<T = any>(pageNo: number, pageSize: number, array: T[]): T[] {
  const offset = (pageNo - 1) * Number(pageSize);
  return offset + Number(pageSize) >= array.length
    ? array.slice(offset, array.length)
    : array.slice(offset, offset + Number(pageSize));
}

export interface requestParams {
  method: string;
  body: any;
  headers?: { authorization?: string };
  query: any;
}

/**
 * @description 本函数用于从request数据中获取token，请根据项目的实际情况修改
 *
 */
export function getRequestToken({ headers }: requestParams): string | undefined {
  return headers?.authorization;
}
