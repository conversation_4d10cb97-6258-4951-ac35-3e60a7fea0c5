# Cesium 配置修复脚本
# 用于快速修复 vite-plugin-cesium 相关问题

Write-Host "🚀 开始修复 Cesium 配置..." -ForegroundColor Green

# 1. 清理 Vite 缓存
Write-Host "🧹 清理 Vite 缓存..." -ForegroundColor Yellow
if (Test-Path "node_modules\.cache") {
    Remove-Item -Recurse -Force "node_modules\.cache"
    Write-Host "✅ 已删除 node_modules\.cache" -ForegroundColor Green
}

if (Test-Path "node_modules\.vite") {
    Remove-Item -Recurse -Force "node_modules\.vite"
    Write-Host "✅ 已删除 node_modules\.vite" -ForegroundColor Green
}

# 2. 检查配置文件
Write-Host "🔍 检查配置文件..." -ForegroundColor Yellow

$configFile = "public\resource\config\index"
if (Test-Path $configFile) {
    Write-Host "✅ 本地配置文件存在: $configFile" -ForegroundColor Green
} else {
    Write-Host "❌ 本地配置文件不存在: $configFile" -ForegroundColor Red
    Write-Host "   请确保该文件存在并包含 enginePath 和 engineToken 配置" -ForegroundColor Yellow
}

$viteConfig = "vite.config.ts"
if (Test-Path $viteConfig) {
    Write-Host "✅ Vite 配置文件存在: $viteConfig" -ForegroundColor Green
} else {
    Write-Host "❌ Vite 配置文件不存在: $viteConfig" -ForegroundColor Red
}

# 3. 检查依赖
Write-Host "📦 检查关键依赖..." -ForegroundColor Yellow

$packageJson = Get-Content "package.json" | ConvertFrom-Json
$dependencies = $packageJson.dependencies
$devDependencies = $packageJson.devDependencies

$requiredDeps = @("cesium", "vue-cesium")
$requiredDevDeps = @("vite-plugin-cesium")

foreach ($dep in $requiredDeps) {
    if ($dependencies.$dep) {
        Write-Host "✅ 依赖存在: $dep@$($dependencies.$dep)" -ForegroundColor Green
    } else {
        Write-Host "❌ 缺少依赖: $dep" -ForegroundColor Red
    }
}

foreach ($dep in $requiredDevDeps) {
    if ($devDependencies.$dep) {
        Write-Host "✅ 开发依赖存在: $dep@$($devDependencies.$dep)" -ForegroundColor Green
    } else {
        Write-Host "❌ 缺少开发依赖: $dep" -ForegroundColor Red
    }
}

# 4. 检查 Node.js 版本
Write-Host "🔧 检查 Node.js 版本..." -ForegroundColor Yellow
$nodeVersion = node --version
Write-Host "Node.js 版本: $nodeVersion" -ForegroundColor Cyan

if ($nodeVersion -match "v(\d+)\.") {
    $majorVersion = [int]$matches[1]
    if ($majorVersion -ge 16) {
        Write-Host "✅ Node.js 版本符合要求 (>= 16)" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Node.js 版本可能过低，建议使用 16+ 版本" -ForegroundColor Yellow
    }
}

# 5. 提供修复建议
Write-Host "`n💡 修复建议:" -ForegroundColor Cyan
Write-Host "1. 如果仍有问题，请运行: npm run clean:cache" -ForegroundColor White
Write-Host "2. 重新安装依赖: npm install 或 yarn install" -ForegroundColor White
Write-Host "3. 启动开发服务器: npm run dev 或 yarn serve" -ForegroundColor White
Write-Host "4. 访问地图页面测试: http://localhost:6060/demo/standard/map" -ForegroundColor White
Write-Host "5. 查看详细配置指南: CESIUM_SETUP_GUIDE.md" -ForegroundColor White

Write-Host "`n✨ 修复脚本执行完成！" -ForegroundColor Green
