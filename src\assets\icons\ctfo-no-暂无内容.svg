<?xml version="1.0" encoding="UTF-8"?>
<svg width="160px" height="120px" viewBox="0 0 160 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 23</title>
    <defs>
        <linearGradient x1="50%" y1="50%" x2="84.4964268%" y2="51.9993268%" id="linearGradient-1">
            <stop stop-color="#187EE3" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#187EE3" stop-opacity="0.15" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="44.9702756%" x2="96.9521421%" y2="55.0297244%" id="linearGradient-2">
            <stop stop-color="#EBF7FF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#3E9FFF" stop-opacity="0.3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.0384615%" id="linearGradient-6">
            <stop stop-color="#4EA9FF" offset="0%"></stop>
            <stop stop-color="#F1F9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.0384615%" id="linearGradient-7">
            <stop stop-color="#9ACEFF" offset="0%"></stop>
            <stop stop-color="#F1F9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="10.5978491%" x2="50.6227551%" y2="88.3057256%" id="linearGradient-8">
            <stop stop-color="#F1F9FF" offset="0%"></stop>
            <stop stop-color="#93C9FF" offset="100%"></stop>
        </linearGradient>
        <path d="M8.46767726,11.0382308 L8.46767726,68.5274503 C12.7343362,71.2012616 18.581964,74.1118431 26.0105606,77.2591949 C33.4391572,80.4065466 40.9189462,83.1425322 48.4499275,85.4671515 L48.4499275,26.3921925 L8.46767726,11.0382308 Z" id="path-9"></path>
        <filter x="-2.5%" y="-1.3%" width="105.0%" height="102.7%" filterUnits="objectBoundingBox" id="filter-10">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.9 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.64600756%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#B8DCFF" offset="100%"></stop>
        </linearGradient>
        <path d="M48.4499275,85.4671515 L48.4499275,26.3921925 L105.404817,11.0382308 L105.404817,66.0606684 C97.2504974,70.4329866 88.2541159,74.2952931 78.4156725,77.647588 C68.5772291,80.9998829 58.5886474,83.606404 48.4499275,85.4671515 Z" id="path-12"></path>
        <filter x="-1.8%" y="-1.3%" width="103.5%" height="102.7%" filterUnits="objectBoundingBox" id="filter-13">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.90034965 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="13.4106264%" x2="50%" y2="74.9020456%" id="linearGradient-14">
            <stop stop-color="#AED8FF" offset="0%"></stop>
            <stop stop-color="#66B5FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="13.4106264%" x2="50%" y2="74.9020456%" id="linearGradient-15">
            <stop stop-color="#ECF6FF" offset="0%"></stop>
            <stop stop-color="#D4E9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="13.4106264%" x2="50%" y2="74.9020456%" id="linearGradient-16">
            <stop stop-color="#AED8FF" offset="0%"></stop>
            <stop stop-color="#66B5FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="47.2333637%" y1="36.7703257%" x2="55.5039465%" y2="60.0744711%" id="linearGradient-17">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#D4E9FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图标" transform="translate(-1370.000000, -6150.000000)">
            <g id="编组-9" transform="translate(60.000000, 5991.000000)">
                <g id="编组-23" transform="translate(1310.000000, 159.000000)">
                    <rect id="矩形" stroke="#FFFFFF" opacity="0" x="0.5" y="0.5" width="159" height="119"></rect>
                    <g id="编组-31" transform="translate(0.000000, 9.000000)">
                        <ellipse id="椭圆形" fill="url(#linearGradient-1)" cx="80" cy="81.6107383" rx="80" ry="19.3288591"></ellipse>
                        <polygon id="路径-13" fill="url(#linearGradient-2)" points="75.2062732 85.2067661 19.5595399 78.8985654 49.2940917 67.5574954"></polygon>
                        <path d="M21.6784051,65.4481376 C25.7861384,69.6249156 27.840005,73.6286491 27.840005,77.4593379 C27.840005,81.0081521 26.0772781,83.6857405 22.5518244,85.4921031 L22.5503356,92.3489933 L20.4026846,92.3489933 L20.4024597,85.7393727 C16.8029014,85.01509 15.0031223,82.2550784 15.0031223,77.4593379 C15.0031223,72.1269678 17.2282166,68.1232344 21.6784051,65.4481376 Z" id="形状结合" fill="url(#linearGradient-3)"></path>
                        <path d="M131.165636,52.5622315 C134.586133,56.0402214 136.296381,59.3741176 136.296381,62.5639201 C136.296381,65.5189752 134.828595,67.7485787 131.893023,69.2527305 L131.89169,74.9624891 L130.103348,74.9624891 L130.103315,69.4587101 C127.105871,68.8556501 125.607149,66.5573867 125.607149,62.5639201 C125.607149,58.1236724 127.459978,54.7897762 131.165636,52.5622315 Z" id="形状结合" fill="url(#linearGradient-4)"></path>
                        <path d="M33.4045449,48.2669295 C36.1378046,51.0461313 37.5044345,53.7101902 37.5044345,56.2591063 C37.5044345,58.620697 36.3312966,60.4024655 33.9850208,61.6044119 L33.9847219,66.166589 L32.5556885,66.166589 L32.5544517,61.7683704 C30.1600531,61.2861003 28.9628538,59.4496789 28.9628538,56.2591063 C28.9628538,52.710981 30.4434175,50.0469221 33.4045449,48.2669295 Z" id="形状结合" fill="url(#linearGradient-5)"></path>
                        <g id="编组-35" transform="translate(27.000000, 0.000000)">
                            <polygon id="路径-57" fill="url(#linearGradient-6)" points="8.46767726 11.0382308 54.8000661 0 54.8000661 41.5619743 8.46767726 33.544062"></polygon>
                            <polygon id="路径-58" fill="url(#linearGradient-7)" points="54.8000661 0 105.404817 11.0382308 105.404817 44.9622898 54.8000661 50.6883404"></polygon>
                            <g id="路径-14">
                                <use fill="url(#linearGradient-8)" fill-rule="evenodd" xlink:href="#path-9"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                            </g>
                            <g id="路径-56">
                                <use fill="url(#linearGradient-11)" fill-rule="evenodd" xlink:href="#path-12"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                            </g>
                            <polygon id="路径-59" fill="url(#linearGradient-14)" points="9.69045538 14.7065651 2.96183655 29.5894514 40.0927334 50.267562 48.4499275 26.3921925"></polygon>
                            <polygon id="路径-59" fill="url(#linearGradient-15)" points="8.46767726 11.0382308 0 28.0046518 38.8699553 46.5992277 48.4499275 26.3921925"></polygon>
                            <path d="M49.1538431,28.8187292 C50.5932527,40.5503476 51.6615229,49.5747583 52.3586535,55.8919614 C72.2756263,49.0469897 90.5858892,41.3962637 107.289442,32.9397836 L102.827811,13.483787 L49.1538431,28.8187292 Z" id="路径-60" fill="url(#linearGradient-16)"></path>
                            <path d="M48.4499275,26.3921925 C51.4413575,36.467612 53.2856379,44.6639233 53.9827686,50.9811264 C73.8997413,44.1361547 92.4972925,37.7928607 109.775422,31.9512444 L105.273368,11.0382308 L48.4499275,26.3921925 Z" id="路径-60" fill="url(#linearGradient-17)"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>