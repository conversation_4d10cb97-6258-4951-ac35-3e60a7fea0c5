<?xml version="1.0" encoding="UTF-8"?>
<svg width="160px" height="120px" viewBox="0 0 160 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 24</title>
    <defs>
        <linearGradient x1="50%" y1="50%" x2="84.4964268%" y2="51.9993268%" id="linearGradient-1">
            <stop stop-color="#187EE3" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#187EE3" stop-opacity="0.15" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="44.1902461%" x2="96.9521421%" y2="55.8097539%" id="linearGradient-2">
            <stop stop-color="#EBF7FF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#3E9FFF" stop-opacity="0.3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="5.44913981%" x2="13.1956399%" y2="41.694579%" id="linearGradient-6">
            <stop stop-color="#ACD6FF" offset="0%"></stop>
            <stop stop-color="#3E9FFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="5.64600756%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#E9F4FF" offset="0%"></stop>
            <stop stop-color="#EEF7FF" offset="11.7378715%"></stop>
            <stop stop-color="#B0D8FF" offset="100%"></stop>
        </linearGradient>
        <path d="M36.6365334,80.6369124 L36.6365334,70.8797938 C36.6365334,68.6706548 34.8456724,66.8797938 32.6365334,66.8797938 C32.5573022,66.8797938 32.4780886,66.8821479 32.3989972,66.886853 L16.7565126,67.8174093 C13.4486521,68.0141907 10.6075784,65.4921628 10.410797,62.1843023 C10.4037394,62.0656653 10.4002083,61.9468448 10.4002083,61.827998 L10.4002083,21.4082694 C10.4002083,17.4164139 13.3429133,14.0354356 17.2966031,13.4847565 L72.2854972,5.825776 C76.6615325,5.21627167 80.703111,8.26964831 81.3126153,12.6456836 C81.3635457,13.0113464 81.3891025,13.3800962 81.3891025,13.7492888 L81.3891025,53.4841206 C81.3891025,56.4037762 79.2874468,58.8995892 76.4103983,59.3965613 L53.9315203,63.2794899 C53.0707337,63.4281794 52.2822844,63.8545614 51.6866178,64.4935007 L36.6365334,80.6369124 L36.6365334,80.6369124 Z" id="path-8"></path>
        <filter x="-2.1%" y="0.9%" width="102.8%" height="101.1%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.454382622   0 0 0 0 0.728604827   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-2.1%" y="0.9%" width="102.8%" height="101.1%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.90034965 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <circle id="path-11" cx="30.2088389" cy="39.9494911" r="5.77652712"></circle>
        <filter x="-4.3%" y="-4.3%" width="108.7%" height="117.3%" filterUnits="objectBoundingBox" id="filter-12">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-8.7%" y="-8.7%" width="117.3%" height="126.0%" filterUnits="objectBoundingBox" id="filter-13">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <circle id="path-14" cx="46.3831149" cy="38.2165329" r="5.77652712"></circle>
        <filter x="-4.3%" y="-4.3%" width="108.7%" height="117.3%" filterUnits="objectBoundingBox" id="filter-15">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-8.7%" y="-8.7%" width="117.3%" height="126.0%" filterUnits="objectBoundingBox" id="filter-16">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <circle id="path-17" cx="62.5573908" cy="35.9059221" r="5.77652712"></circle>
        <filter x="-4.3%" y="-4.3%" width="108.7%" height="117.3%" filterUnits="objectBoundingBox" id="filter-18">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-8.7%" y="-8.7%" width="117.3%" height="126.0%" filterUnits="objectBoundingBox" id="filter-19">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.64600756%" x2="50%" y2="100%" id="linearGradient-20">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFD4B1" offset="99.6967876%"></stop>
        </linearGradient>
        <path d="M83.9492316,85.3907042 L77.5930653,77.6560157 C77.2493696,77.2377793 76.7508138,76.9767301 76.2112842,76.9325001 L68.2262925,76.2778995 C66.1508335,76.1077557 64.5531117,74.3736946 64.5531117,72.2912732 L64.5531117,55.4734678 C64.5531117,53.8166135 65.8962574,52.4734678 67.5531117,52.4734678 C67.6510339,52.4734678 67.7488973,52.4782622 67.8463506,52.4878337 L91.2132671,54.7828539 C93.261124,54.9839875 94.8222818,56.7059888 94.8222818,58.7636993 L94.8222818,76.1174402 C94.8222818,77.2220097 93.9268513,78.1174402 92.8222818,78.1174402 C92.7878869,78.1174402 92.7534977,78.1165529 92.7191486,78.1147793 L85.9564186,77.7655836 C84.8533186,77.7086246 83.9129051,78.5566895 83.8559462,79.6597894 C83.8535846,79.7055253 83.8527946,79.7513288 83.8535777,79.797119 L83.9492316,85.3907042 L83.9492316,85.3907042 Z" id="path-21"></path>
        <filter x="-1.7%" y="-3.6%" width="106.6%" height="105.1%" filterUnits="objectBoundingBox" id="filter-22">
            <feOffset dx="1" dy="-1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.69055649   0 0 0 0 0.454382622  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-1.7%" y="-3.6%" width="106.6%" height="105.1%" filterUnits="objectBoundingBox" id="filter-23">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.90034965 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <circle id="path-24" cx="72.6663133" cy="65.0773841" r="2.59943721"></circle>
        <filter x="-9.6%" y="-9.6%" width="119.2%" height="138.5%" filterUnits="objectBoundingBox" id="filter-25">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-19.2%" y="-19.2%" width="138.5%" height="157.7%" filterUnits="objectBoundingBox" id="filter-26">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <circle id="path-27" cx="79.5981458" cy="65.6550368" r="2.59943721"></circle>
        <filter x="-9.6%" y="-9.6%" width="119.2%" height="138.5%" filterUnits="objectBoundingBox" id="filter-28">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-19.2%" y="-19.2%" width="138.5%" height="157.7%" filterUnits="objectBoundingBox" id="filter-29">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <circle id="path-30" cx="86.5299784" cy="66.2326895" r="2.59943721"></circle>
        <filter x="-9.6%" y="-9.6%" width="119.2%" height="138.5%" filterUnits="objectBoundingBox" id="filter-31">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-19.2%" y="-19.2%" width="138.5%" height="157.7%" filterUnits="objectBoundingBox" id="filter-32">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图标" transform="translate(-1690.000000, -6150.000000)">
            <g id="编组-9" transform="translate(60.000000, 5991.000000)">
                <g id="编组-24" transform="translate(1630.000000, 159.000000)">
                    <rect id="矩形" stroke="#FFFFFF" opacity="0" x="0.5" y="0.5" width="159" height="119"></rect>
                    <g id="编组-31" transform="translate(0.000000, 9.000000)">
                        <ellipse id="椭圆形" fill="url(#linearGradient-1)" cx="80" cy="81.6107383" rx="80" ry="19.3288591"></ellipse>
                        <path d="M66.7383479,81.4911218 L14.4452963,75.6770889 C22.743383,71.6817225 29.2565248,69.0149666 33.9847219,67.6768213 C38.7129189,66.3386759 46.3466788,65.0016623 56.8860015,63.6657805 L66.7383479,81.4911218 Z" id="路径-13" fill="url(#linearGradient-2)"></path>
                        <path d="M21.6784051,65.4481376 C25.7861384,69.6249156 27.840005,73.6286491 27.840005,77.4593379 C27.840005,81.0081521 26.0772781,83.6857405 22.5518244,85.4921031 L22.5503356,92.3489933 L20.4026846,92.3489933 L20.4024597,85.7393727 C16.8029014,85.01509 15.0031223,82.2550784 15.0031223,77.4593379 C15.0031223,72.1269678 17.2282166,68.1232344 21.6784051,65.4481376 Z" id="形状结合" fill="url(#linearGradient-3)"></path>
                        <path d="M144.165636,52.5622315 C147.586133,56.0402214 149.296381,59.3741176 149.296381,62.5639201 C149.296381,65.5189752 147.828595,67.7485787 144.893023,69.2527305 L144.89169,74.9624891 L143.103348,74.9624891 L143.103315,69.4587101 C140.105871,68.8556501 138.607149,66.5573867 138.607149,62.5639201 C138.607149,58.1236724 140.459978,54.7897762 144.165636,52.5622315 Z" id="形状结合" fill="url(#linearGradient-4)"></path>
                        <path d="M33.4045449,48.2669295 C36.1378046,51.0461313 37.5044345,53.7101902 37.5044345,56.2591063 C37.5044345,58.620697 36.3312966,60.4024655 33.9850208,61.6044119 L33.9847219,66.166589 L32.5556885,66.166589 L32.5544517,61.7683704 C30.1600531,61.2861003 28.9628538,59.4496789 28.9628538,56.2591063 C28.9628538,52.710981 30.4434175,50.0469221 33.4045449,48.2669295 Z" id="形状结合" fill="url(#linearGradient-5)"></path>
                        <g id="编组-36" transform="translate(31.000000, 0.000000)">
                            <path d="M13.2616255,66.0678574 C4.42054183,63.3005541 0,58.2528991 0,50.9248926 C0,39.7460451 0,28.5671976 0,17.3883501 C1.162419e-15,12.3751672 3.71196863,8.13671977 8.68137661,7.47566975 L62.1801588,0.359053157 C63.9303009,0.126242432 65.7082988,0.478930151 67.2370622,1.36215202 L77.8814554,7.51180294 L77.8814554,7.51180294 L36.5876836,61.4835222 L36.0588807,80.0592597 L27.4513653,75.387438 L27.4513653,64.9806939 L13.2616255,66.0678574 Z" id="路径-62" fill="#B8DFFF"></path>
                            <path d="M13.8392782,68.3321515 C4.99819454,65.683328 0.577652712,60.6949131 0.577652712,53.3669066 C0.577652712,41.5666053 0.577652712,29.7663041 0.577652712,17.9660028 C0.577652712,12.9528199 4.28962134,8.71437248 9.25902932,8.05332246 L62.7578115,0.936705869 C64.5079536,0.703895144 66.2859515,1.05658286 67.8147149,1.93980473 L78.4591081,8.08945565 L78.4591081,8.08945565 L37.1653363,62.061175 L35.7383479,81.4911218 L28.029018,75.9650907 L28.029018,67.4162197 L13.8392782,68.3321515 Z" id="路径-62" fill="url(#linearGradient-6)"></path>
                            <g id="路径-61">
                                <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                                <use fill="url(#linearGradient-7)" fill-rule="evenodd" xlink:href="#path-8"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-8"></use>
                            </g>
                            <g id="椭圆形">
                                <use fill="black" fill-opacity="1" filter="url(#filter-12)" xlink:href="#path-11"></use>
                                <use fill="#3E9FFF" fill-rule="evenodd" xlink:href="#path-11"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-11"></use>
                            </g>
                            <g id="椭圆形">
                                <use fill="black" fill-opacity="1" filter="url(#filter-15)" xlink:href="#path-14"></use>
                                <use fill="#3E9FFF" fill-rule="evenodd" xlink:href="#path-14"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-16)" xlink:href="#path-14"></use>
                            </g>
                            <g id="椭圆形">
                                <use fill="black" fill-opacity="1" filter="url(#filter-18)" xlink:href="#path-17"></use>
                                <use fill="#3E9FFF" fill-rule="evenodd" xlink:href="#path-17"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-19)" xlink:href="#path-17"></use>
                            </g>
                            <path d="M66.8215231,51.8887448 L70.4360807,50.5928643 C70.9740741,50.3999845 71.547154,50.3246329 72.1167212,50.3718846 L94.4133837,52.2216325 C97.0056321,52.4366874 99,54.6033609 99,57.2045146 C99,62.4250898 99,67.645665 99,72.8662402 C99,75.8295425 97.12839,77.5289178 93.3851699,77.9643661 L87.2592135,77.9643661 L87.2592135,82.2470219 L83.9492316,85.3907042 L66.8215231,51.8887448 Z" id="路径-64" fill="#FF9947"></path>
                            <g id="路径-63">
                                <use fill="black" fill-opacity="1" filter="url(#filter-22)" xlink:href="#path-21"></use>
                                <use fill="url(#linearGradient-20)" fill-rule="evenodd" xlink:href="#path-21"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-23)" xlink:href="#path-21"></use>
                            </g>
                            <g id="椭圆形">
                                <use fill="black" fill-opacity="1" filter="url(#filter-25)" xlink:href="#path-24"></use>
                                <use fill="#FF9E4F" fill-rule="evenodd" xlink:href="#path-24"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-26)" xlink:href="#path-24"></use>
                            </g>
                            <g id="椭圆形">
                                <use fill="black" fill-opacity="1" filter="url(#filter-28)" xlink:href="#path-27"></use>
                                <use fill="#FF9E4F" fill-rule="evenodd" xlink:href="#path-27"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-29)" xlink:href="#path-27"></use>
                            </g>
                            <g id="椭圆形">
                                <use fill="black" fill-opacity="1" filter="url(#filter-31)" xlink:href="#path-30"></use>
                                <use fill="#FF9E4F" fill-rule="evenodd" xlink:href="#path-30"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-32)" xlink:href="#path-30"></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>