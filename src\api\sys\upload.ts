/*
 * @Description: 上传文件接口
 * @Author: XIAOLIJUN
 * @Date: 2022-07-07 19:41:02
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2022-10-24 15:26:35
 */
/*
 * @Description: 上传文件接口
 * @Author: XIAOLIJUN
 * @Date: 2022-09-28 16:16:43
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2022-10-24 15:26:21
 */
import { UploadApiResult } from './model/uploadModel';
import { defHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { isUrl } from '/@/utils/is';

const { apiUrl = '', uploadUrl = '' } = useGlobSetting();

/**
 * @description: Upload interface
 */
export function uploadApi(
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) {
  const url = isUrl(uploadUrl) ? uploadUrl : apiUrl + uploadUrl;
  return defHttp.uploadFile<UploadApiResult>({ url, onUploadProgress }, params);
}
