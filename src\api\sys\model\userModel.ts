/*
 * @Description: 用户接口类型
 * @Author: XIAOLIJUN
 * @Date: 2022-07-05 16:23:53
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2022-07-05 18:11:13
 */
/**
 * @description: 登录接口参数
 */
export interface LoginParams {
  username: string;
  password: string;
}

export interface RoleInfo {
  roleName: string;
  value: string;
}

/**
 * @description: 登录界面返回值
 */
export interface LoginResultModel {
  userId: string | number;
  token: string;
  role: RoleInfo;
}

/**
 * @description: 获取用户信息返回值
 */
export interface GetUserInfoModel {
  roles: RoleInfo[];
  // 用户id
  userId: string | number;
  // 用户名
  username: string;
  // 真实名字
  realName: string;
  // 头像
  avatar: string;
  // 介绍
  desc?: string;
}
