/*
 * @Description: 大屏缩放适配 Hook
 * @Author: XIAOLIJUN
 * @Date: 2025-08-19 10:05:00
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2025-08-19 16:33:01
 */
import { ref, computed, onMounted, onUnmounted, nextTick, readonly } from 'vue';
import type { Ref, ComputedRef } from 'vue';

export interface ScreenScaleOptions {
  designWidth?: number; // 设计稿宽度
  designHeight?: number; // 设计稿高度
  debounceTime?: number; // 防抖时间
  enableMapResize?: boolean; // 是否启用地图缩放适配
}

export interface ScreenScaleReturn {
  scale: Readonly<Ref<number>>;
  screenWidth: Readonly<Ref<number>>;
  screenHeight: Readonly<Ref<number>>;
  containerStyle: ComputedRef<Record<string, string>>;
  updateScale: () => void;
  getScale: () => number;
  getDesignSize: () => { width: number; height: number };
  onMapResize: (callback: (scale: number) => void) => void;
  offMapResize: (callback: (scale: number) => void) => void;
}

/**
 * 大屏缩放适配 Hook
 * @param options 配置选项
 * @returns 缩放相关的响应式数据和方法
 */
export function useScreenScale(options: ScreenScaleOptions = {}): ScreenScaleReturn {
  const {
    designWidth = 8600,
    designHeight = 3600,
    debounceTime = 100,
    enableMapResize = true,
  } = options;

  // 响应式数据
  const scale = ref(1);
  const screenWidth = ref(window.innerWidth);
  const screenHeight = ref(window.innerHeight);

  // 地图缩放回调函数集合
  const mapResizeCallbacks = new Set<(scale: number) => void>();

  // 防抖定时器
  let debounceTimer: NodeJS.Timeout | null = null;

  // 计算缩放比例
  const calculateScale = (): number => {
    const scaleX = screenWidth.value / designWidth;
    const scaleY = screenHeight.value / designHeight;
    // 使用较小的缩放比例，确保内容完全显示
    return Math.min(scaleX, scaleY);
  };

  // 容器样式
  const containerStyle = computed(() => {
    const currentScale = scale.value;
    return {
      width: `${designWidth}px`,
      height: `${designHeight}px`,
      transform: `scale(${currentScale})`,
      transformOrigin: 'center center',
      position: 'absolute' as const,
      left: '50%',
      top: '50%',
      marginLeft: `${-designWidth / 2}px`,
      marginTop: `${-designHeight / 2}px`,
    };
  });

  // 触发地图重新渲染
  const triggerMapResize = () => {
    if (enableMapResize && mapResizeCallbacks.size > 0) {
      nextTick(() => {
        mapResizeCallbacks.forEach((callback) => {
          try {
            callback(scale.value);
          } catch (error) {
            console.warn('地图缩放回调执行失败:', error);
          }
        });
      });
    }
  };

  // 更新尺寸和缩放
  const updateScale = () => {
    screenWidth.value = window.innerWidth;
    screenHeight.value = window.innerHeight;
    const newScale = calculateScale();

    // 只有缩放比例发生变化时才更新
    if (Math.abs(scale.value - newScale) > 0.001) {
      scale.value = newScale;
      triggerMapResize();
    }
  };

  // 防抖处理的窗口大小变化监听
  const handleResize = () => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    debounceTimer = setTimeout(() => {
      updateScale();
      debounceTimer = null;
    }, debounceTime);
  };

  // 添加地图缩放回调
  const onMapResize = (callback: (scale: number) => void) => {
    mapResizeCallbacks.add(callback);
  };

  // 移除地图缩放回调
  const offMapResize = (callback: (scale: number) => void) => {
    mapResizeCallbacks.delete(callback);
  };

  // 获取当前缩放比例
  const getScale = (): number => scale.value;

  // 获取设计稿尺寸
  const getDesignSize = () => ({ width: designWidth, height: designHeight });

  // 生命周期
  onMounted(() => {
    updateScale();
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    mapResizeCallbacks.clear();
  });

  return {
    scale: readonly(scale),
    screenWidth: readonly(screenWidth),
    screenHeight: readonly(screenHeight),
    containerStyle,
    updateScale,
    getScale,
    getDesignSize,
    onMapResize,
    offMapResize,
  };
}

/**
 * Cesium 地图缩放适配器
 * 专门处理 Cesium 地图在大屏缩放时的适配问题
 */
export function useCesiumMapScale(viewer: any, screenScale: ScreenScaleReturn) {
  const handleMapResize = (scale: number) => {
    if (viewer && viewer.resize) {
      try {
        // 强制 Cesium 重新计算画布尺寸
        viewer.resize();

        // 如果缩放比例过小，可能需要调整地图的渲染质量
        if (scale < 0.5) {
          // 降低渲染质量以提高性能
          viewer.resolutionScale = Math.max(0.5, scale);
        } else {
          viewer.resolutionScale = 1.0;
        }

        // 强制重新渲染
        viewer.scene.requestRender();
      } catch (error) {
        console.warn('Cesium 地图缩放适配失败:', error);
      }
    }
  };

  // 注册地图缩放回调
  screenScale.onMapResize(handleMapResize);

  // 返回清理函数
  return () => {
    screenScale.offMapResize(handleMapResize);
  };
}

/**
 * 通用地图缩放适配器
 * 适用于其他地图库（如高德、百度等）
 */
export function useGenericMapScale(
  mapInstance: any,
  resizeMethod = 'resize',
  screenScale: ScreenScaleReturn,
) {
  const handleMapResize = () => {
    if (mapInstance && typeof mapInstance[resizeMethod] === 'function') {
      try {
        // 延迟执行，确保 DOM 更新完成
        setTimeout(() => {
          mapInstance[resizeMethod]();
        }, 50);
      } catch (error) {
        console.warn(`地图 ${resizeMethod} 方法执行失败:`, error);
      }
    }
  };

  // 注册地图缩放回调
  screenScale.onMapResize(handleMapResize);

  // 返回清理函数
  return () => {
    screenScale.offMapResize(handleMapResize);
  };
}
