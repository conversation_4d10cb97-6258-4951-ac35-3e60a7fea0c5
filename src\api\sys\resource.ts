/*
 * @Description: 静态资源加载
 * @Author: XIAOLIJUN
 * @Date: 2022-07-11 10:41:21
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2025-04-27 14:35:29
 */
import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';

enum Api {
  LoadLocalConfig = './resource/config/index',
}
/**
 * @description: 加载静态资源配置
 */
export function loadLocalConfig(mode: ErrorMessageMode = 'message') {
  return defHttp.get(
    { url: Api.LoadLocalConfig },
    { errorMessageMode: mode, apiUrl: undefined, withToken: false },
  );
}
