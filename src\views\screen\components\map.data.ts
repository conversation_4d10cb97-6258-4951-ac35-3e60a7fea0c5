/*
 * @Description:
 * @Author: XIAOLIJUN
 * @Date: 2025-08-20 15:33:57
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2025-08-20 19:53:55
 */
export const MaterialAppearance = {
  type: 'MaterialAppearance',
  options: {
    material: {
      fabric: {
        type: 'VcLineFlow',
        uniforms: {
          image: './resource/images/fence.png',
          axisY: true,
          color: '#217297ff',
          repeat: { x: 5, y: 1 },
          speed: 10,
          mixt: true,
        },
      },
    },
  },
};

export const FlowingShaderMaterial = {
  fabric: {
    type: 'FlowingShaderMaterial',
    uniforms: {
      color: new Cesium.Color(0.0, 1.0, 1.0, 1.0), // 默认亮青色
      flowOffset: 0.0, // 流动偏移
      gapLength: 0.1, // 一个光段的长度占整体的比例
    },
    source: `
            czm_material czm_get  / / * Material(czm_materialInput materialInput) {
                czm_material material = czm_getDefaultMaterial(materialInput);
                float s = materialInput.s; // 纹理横坐标，范围0到1，沿着路线方向
                float speed = flowOffset; // 动画速度
                float alpha = 0.0;
                float segmentStart = fract(s * (1.0 / gapLength) - speed); // 计算当前位置在光段内的相对位置
                // 简单的流光效果：让某个范围内的颜色亮度最高，然后向两边衰减
                float glowWidth = 0.05; // 流光宽度
                if (segmentStart > 0.0 && segmentStart < glowWidth) {
                    alpha = segmentStart / glowWidth; // 前半段渐变亮
                } else if (segmentStart >= glowWidth && segmentStart < gapLength) {
                    alpha = (gapLength - segmentStart) / (gapLength - glowWidth); // 后半段渐变暗
                }
                // 辉光效果
                material.alpha = alpha * color.a; // alpha由流光强度和基础颜色alpha决定
                material.diffuse = color.rgb;
                material.emission = color.rgb * alpha * 2.0; // 辉光，让亮的地方更亮
                return material;
            }
        `,
  },
  translucent: function () {
    return true;
  },
};
