/*
 * @Description: icon 生成配置
 * @Author: XIAOLIJUN
 * @Date: 2022-07-05 16:23:53
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2023-09-28 14:51:00
 */
import { generateAntColors, primaryColor } from '../config/themeConfig';
import { getThemeVariables } from 'ant-design-vue/dist/theme';
import { resolve } from 'path';

/**
 * less global variable
 */
export function generateModifyVars(dark = false) {
  const palettes = generateAntColors(primaryColor);
  const primary = palettes[5];

  const primaryColorObj: Record<string, string> = {};

  for (let index = 0; index < 10; index++) {
    primaryColorObj[`primary-${index + 1}`] = palettes[index];
  }

  const modifyVars = getThemeVariables({ dark });
  return {
    ...modifyVars,
    //全局导入，避免单独导入每个样式文件
    // reference:避免重复引用
    hack: `${modifyVars.hack} @import (reference) "${resolve('src/design/config.less')}";`,
    'primary-color': primary,
    ...primaryColorObj,
    'info-color': primary,
    'processing-color': primary,
    'success-color': '#00B14B', //  Success color
    'error-color': '#DE0E00', //  False color
    'warning-color': '#E1BB01', //   Warning color
    //'border-color-base': '#EEEEEE',
    'heading-color': '#333333',
    'text-color': '#666666',
    'font-size-base': '14px', //  Main font size
    'border-radius-base': '2px', //  Component/float fillet
    'link-color': primary, //   Link color
    'app-content-background': '#fafafa', //   Link color
  };
}
