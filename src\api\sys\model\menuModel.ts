/*
 * @Description: 菜单接口类型
 * @Author: XIAOLIJUN
 * @Date: 2022-07-05 16:23:53
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2022-07-05 18:11:56
 */
import type { RouteMeta } from 'vue-router';
export interface RouteItem {
  path: string;
  component: any;
  meta: RouteMeta;
  name?: string;
  alias?: string | string[];
  redirect?: string;
  caseSensitive?: boolean;
  children?: RouteItem[];
}

/**
 * @description: 获取菜单返回值
 */
export type getMenuListResultModel = RouteItem[];
