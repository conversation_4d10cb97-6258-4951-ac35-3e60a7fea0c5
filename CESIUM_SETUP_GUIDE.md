# Cesium + vite-plugin-cesium 配置指南

## 问题描述
在使用 vite-plugin-cesium 加载 Cesium 时遇到以下问题：
1. ES2015 目标环境不支持 BigInt 字面量、async generator 函数等现代 JavaScript 特性
2. Cesium 依赖优化问题
3. Sourcemap 警告

## 解决方案

### 1. 更新 vite.config.ts 配置

已经应用的关键配置更改：

```typescript
export default ({ command, mode }: ConfigEnv): UserConfig => {
  // ... 其他配置

  return {
    // 设置日志级别，减少不必要的警告
    logLevel: isBuild ? 'info' : 'warn',
    
    // 更新 esbuild 目标环境
    esbuild: {
      target: 'es2020', // 支持现代 JavaScript 特性
      pure: VITE_DROP_CONSOLE ? ['console.log', 'debugger'] : [],
    },
    
    // 更新构建目标环境
    build: {
      target: 'es2020', // 支持现代 JavaScript 特性
      sourcemap: false, // 禁用 sourcemap 以避免警告
      // ... 其他构建配置
    },
    
    // Cesium 插件配置
    plugins: [
      ...createVitePlugins(viteEnv, isBuild),
      cesium({
        rebuildCesium: false, // 禁用重建以避免问题
      }),
    ],
    
    // 优化依赖配置
    optimizeDeps: {
      include: [
        '@vue/runtime-core',
        '@vue/shared',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
      ],
      // 排除 Cesium 相关包的预构建
      exclude: ['cesium', 'vue-cesium', '@zip.js/zip.js', '@spz-loader/core'],
      esbuildOptions: {
        target: 'es2020',
      },
    },
  };
};
```

### 2. 清理缓存

如果仍有问题，请清理 Vite 缓存：

```bash
# 删除缓存目录
rm -rf node_modules/.cache
rm -rf node_modules/.vite

# 或使用项目脚本
npm run clean:cache
```

### 3. 重新安装依赖（如果需要）

```bash
# 删除 node_modules 和锁定文件
rm -rf node_modules
rm -rf pnpm-lock.yaml

# 重新安装
pnpm install
```

### 4. 启动开发服务器

```bash
npm run dev
# 或
yarn serve
```

## 关键配置说明

### ES2020 目标环境
- 将构建目标从 `es2015` 更新为 `es2020`
- 支持 BigInt、async/await、for-await 循环等现代特性
- 兼容现代浏览器

### Cesium 优化排除
- 将 `cesium`、`vue-cesium` 等包从 Vite 预构建中排除
- 避免 Cesium 的复杂依赖导致的优化问题
- 使用 vite-plugin-cesium 处理 Cesium 资源

### Sourcemap 处理
- 在生产构建中禁用 sourcemap
- 减少第三方库 sourcemap 警告
- 不影响开发体验

## 验证配置

1. 启动开发服务器后，访问地图页面：`/demo/standard/map`
2. 检查控制台是否有错误
3. 确认地图能正常渲染
4. 测试地图交互功能

## 常见问题

### Q: 仍然看到 sourcemap 警告
A: 这些警告来自第三方库，不影响功能。可以忽略或通过设置 `logLevel: 'error'` 来隐藏。

### Q: Cesium 资源加载失败
A: 检查 `CESIUM_BASE_URL` 配置，确保指向正确的 Cesium 资源路径。

### Q: 地图不显示
A: 检查网络连接，确认天地图 token 有效，检查浏览器控制台错误信息。

## 相关文件

- `vite.config.ts` - 主要配置文件
- `src/components/MapEngine/` - 地图组件
- `src/views/demo/standard/map/index.vue` - 地图示例页面
