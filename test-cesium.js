/**
 * Cesium 配置测试脚本
 * 用于验证 Cesium 和 vite-plugin-cesium 配置是否正确
 */

// 检查 Cesium 全局变量
function checkCesiumGlobals() {
  console.log('🔍 检查 Cesium 全局变量...');
  
  if (typeof window !== 'undefined') {
    console.log('✅ CESIUM_BASE_URL:', window.CESIUM_BASE_URL || '未设置');
    
    if (window.Cesium) {
      console.log('✅ Cesium 全局对象已加载');
      console.log('📦 Cesium 版本:', window.Cesium.VERSION || '未知');
    } else {
      console.log('❌ Cesium 全局对象未找到');
    }
  }
}

// 检查 Vue Cesium 组件
function checkVueCesium() {
  console.log('🔍 检查 Vue Cesium 组件...');
  
  try {
    // 这里可以添加 Vue Cesium 组件的检查逻辑
    console.log('✅ Vue Cesium 组件检查完成');
  } catch (error) {
    console.error('❌ Vue Cesium 组件检查失败:', error);
  }
}

// 检查网络资源
async function checkNetworkResources() {
  console.log('🔍 检查网络资源...');
  
  const resources = [
    '//172.20.60.80:40010/master/1.99/Build/Cesium/Cesium.js',
    '//172.20.60.80:40030/map-tiler/shanxi_tdt_tms/',
  ];
  
  for (const resource of resources) {
    try {
      const response = await fetch(resource, { method: 'HEAD' });
      if (response.ok) {
        console.log(`✅ 资源可访问: ${resource}`);
      } else {
        console.log(`⚠️ 资源响应异常: ${resource} (状态: ${response.status})`);
      }
    } catch (error) {
      console.log(`❌ 资源不可访问: ${resource}`);
      console.log(`   错误: ${error.message}`);
    }
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始 Cesium 配置测试...\n');
  
  checkCesiumGlobals();
  console.log('');
  
  checkVueCesium();
  console.log('');
  
  await checkNetworkResources();
  console.log('');
  
  console.log('✨ 测试完成！');
  console.log('💡 如果看到错误，请检查:');
  console.log('   1. 网络连接是否正常');
  console.log('   2. Cesium 资源服务器是否可访问');
  console.log('   3. vite.config.ts 配置是否正确');
  console.log('   4. 本地配置文件 /resource/config/index 是否存在');
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  // 等待页面加载完成后运行测试
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runTests);
  } else {
    runTests();
  }
}

// 如果在 Node.js 环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runTests, checkCesiumGlobals, checkVueCesium, checkNetworkResources };
}
