/*
 * @Description: 用户模拟数据接口
 * @Author: XIAOLIJUN
 * @Date: 2022-07-07 19:41:02
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2022-07-10 16:25:46
 */
import { defHttp } from '/@/utils/http/axios';
import { LoginParams, LoginResultModel, GetUserInfoModel } from './model/userModel';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  Login = '/mock/login',
  Logout = '/mock/logout',
  GetUserInfo = '/mock/getUserInfo',
  GetPermCode = '/mock/getPermCode',
  TestRetry = '/mock/testRetry',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>({ url: Api.Login, params }, { errorMessageMode: mode });
}

/**
 * @description: getUserInfo
 */
export function getUserInfo() {
  return defHttp.get<GetUserInfoModel>({ url: Api.GetUserInfo }, { errorMessageMode: 'none' });
}

export function getPermCode() {
  return defHttp.get<string[]>({ url: Api.GetPermCode });
}

export function doLogout() {
  return defHttp.get({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: { isOpenRetry: true, count: 5, waitTime: 1000 },
    },
  );
}
