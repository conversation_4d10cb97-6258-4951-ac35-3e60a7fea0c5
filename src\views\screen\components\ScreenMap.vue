<!--
 * @Description: 大屏地图组件示例
 * @Author: XIAOLIJUN
 * @Date: 2025-08-19 10:10:00
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2025-08-20 19:46:44
-->
<template>
  <div :class="prefixCls">
    <!-- Cesium 地图容器 -->
    <BasicMap
      ref="mapEngineRef"
      :resolutionScale="scale"
      @ready="handleViewerReady"
      @left-click="handleLeftClick"
      @wheel="handleWheel"
      @mouse-move="handleMouseMove"
    >
      <vc-datasource-geojson
        data="./resource/json/220000_full.json"
        stroke="rgba(17, 255, 255, 1)"
        fill="rgba(0, 94, 174, 0.5)"
        :clampToGround="true"
        @ready="handleFullJsonReady"
      />
      <vc-datasource-geojson
        data="./resource/json/220100_220300.json"
        stroke="rgba(17, 255, 255, 1)"
        fill="rgba(0, 94, 174, 0.5)"
        :clampToGround="true"
      />

      <vc-datasource-geojson
        data="./resource/json/220100_220300.json"
        stroke="rgba(17, 255, 255, 1)"
        fill="rgba(2, 239, 255, 0.5)"
        :clampToGround="true"
        @ready="handleGeojsonDataReady"
      />

      <vc-primitive
        v-for="(item, index) in positions"
        :key="'wall_' + index"
        :appearance="MaterialAppearance"
      >
        <vc-geometry-instance :attributes="attributes">
          <vc-geometry-wall :positions="item" />
        </vc-geometry-instance>
      </vc-primitive>
    </BasicMap>

    <!-- 地图加载状态 -->
    <div v-if="!mapReady" :class="`${prefixCls}-loading`">
      <div :class="`${prefixCls}-loading-content`">
        <div :class="`${prefixCls}-loading-spinner`"></div>
        <div :class="`${prefixCls}-loading-text`">地图加载中...</div>
      </div>
    </div>

    <!-- 地图控制面板 -->
    <div v-if="mapReady" :class="`${prefixCls}-controls`">
      <div :class="`${prefixCls}-scale-info`"> 缩放比例: {{ (scale * 100).toFixed(1) }}% </div>
      <div :class="`${prefixCls}-resolution-info`">
        分辨率: {{ screenWidth }} × {{ screenHeight }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onUnmounted, inject, watch } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { BasicMap } from '/@/components/MapEngine';
  import { useCesiumMapScale } from '/@/hooks/web/useScreenScale';
  import { setViewerTheme } from '/@/components/MapEngine/src/tools';
  import { VcDatasourceGeojson, VcPrimitive, VcGeometryWall, VcGeometryInstance } from 'vue-cesium';

  import { MaterialAppearance } from './map.data';
  import { clone } from 'lodash-es';

  const { prefixCls } = useDesign('screen-map');

  // 注入父组件的缩放数据
  const scale = inject<any>('scale', ref(1));
  const screenWidth = inject<any>('screenWidth', ref(1920));
  const screenHeight = inject<any>('screenHeight', ref(1080));
  const onMapResize = inject<any>('onMapResize', () => {});
  const offMapResize = inject<any>('offMapResize', () => {});

  const mapReady = ref(false);
  const mapEngineRef = ref<any>();
  let cesiumViewer: any = null;

  // 地图缩放清理函数
  let cleanupMapScale: (() => void) | null = null;
  const attributes = ref<any>(null);
  const positions = ref<any>([]);

  // const imageryProvider = new SingleColorImageryProvider({
  //   // 你可以将这里设置为任何你想要的颜色，例如 Color.RED, Color.GREEN, Color.fromCssColorString('#FF5733')
  //   color: Color.DARKBLUE,
  // });

  const url =
    'https://webst0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&style=7&scl=1&ltype=11&x={x}&y={y}&z={z}';

  // Cesium Viewer 就绪回调
  const handleViewerReady = ({ viewer }) => {
    mapReady.value = true;

    console.log('Cesium Viewer 就绪', viewer);
    cesiumViewer = viewer;

    attributes.value = {
      color: new Cesium.ColorGeometryInstanceAttribute(
        Math.random(),
        Math.random(),
        Math.random(),
        0.5,
      ),
    };

    // 设置地图缩放适配
    if (onMapResize && typeof onMapResize === 'function') {
      cleanupMapScale = useCesiumMapScale(viewer, {
        onMapResize,
        offMapResize,
        getScale: () => scale.value,
      } as any);
    }

    // 初始化地图设置
    initMapSettings(viewer);
  };

  // 初始化地图设置
  const initMapSettings = (viewer: any) => {
    try {
      // 添加高德地图
      viewer.imageryLayers.removeAll();
      viewer.imageryLayers.addImageryProvider(
        new Cesium.UrlTemplateImageryProvider({
          url,
          subdomains: ['1', '2', '3', '4'],
          maximumLevel: 18,
        }),
      );

      // 设置地图主题
      setViewerTheme(viewer, { filterRGB_R: 0, filterRGB_G: 95, filterRGB_B: 191 });

      viewer.resolutionScale = scale.value;
      // 将整个地球模型隐藏
      // viewer.scene.globe.show = false;
      // viewer.scene.backgroundColor = Cesium.Color.fromBytes(3, 49, 86, 1);
      // viewer.scene.globe.baseColor = Cesium.Color.fromBytes(11, 68, 134, 1);
      // 可选：禁用日照效果，使颜色更纯粹
      viewer.scene.globe.enableLighting = false;
      // 可选：禁用太阳和月亮（如果你完全不需要任何光源除了你自己的）
      viewer.scene.logarithmicDepthBuffer = true; // 使用对数深度缓冲区。启用此选项将减少多视锥中的视锥，提高性能。
      // viewer.scene.globe.depthTestAgainstTerrain = true; // 深度检测(开启解决着色器bug)
      viewer.scene.fxaa = true; // 开启抗锯齿
      viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
      // viewer.scene.screenSpaceCameraController.enableTilt = false; // 禁用倾斜
      viewer.scene.screenSpaceCameraController.maximumZoomDistance = 5000000;

      // 禁用默认的双击行为
      // viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      //   viewer.scene.screenSpaceCameraController.enableInputs &&
      //     viewer.scene.screenSpaceCameraController.enableInputs.doubleClick,
      // );
      fetchGeojsonFull();
    } catch (error) {
      console.error('地图初始化设置失败:', error);
    }
  };

  // 监听缩放变化
  watch(scale, (newScale) => {
    console.log('缩放比例更新:', newScale, !!cesiumViewer);
    if (cesiumViewer && typeof newScale === 'number') {
      // 根据缩放比例调整地图渲染质量
      try {
        cesiumViewer.resolutionScale = newScale;
        console.log('resolutionScale', cesiumViewer.resolutionScale);
        cesiumViewer.scene.requestRender();
        cesiumViewer.resize();
      } catch (error) {
        console.warn('地图缩放调整失败:', error);
      }
    }
  });

  function handleLeftClick(evt) {
    console.log(evt);
  }

  function handleWheel(delta: number) {
    console.log(delta);
  }

  function handleMouseMove({ startPosition, endPosition }) {
    // console.log(startPosition, endPosition);
  }

  const fetchGeojsonFull = () => {
    fetch('./resource/json/220000_full.json')
      .then((response) => response.json())
      .then((data) => {
        const list = data?.features
          .filter(({ properties }) => ['长春市', '四平市'].includes(properties.name))
          .map(({ geometry }) => {
            const pos = geometry.coordinates?.flat(2);
            return pos.map(([lng, lat]) => ({ lng, lat, height: 20000 }));
          });
        positions.value = list;

        console.log('positions', positions.value);
      });
  };

  function handleFullJsonReady({ cesiumObject }) {
    var entities = cesiumObject.entities.values;
    console.log('handleFullJsonReady', entities);
    for (let i = 0; i < entities.length; i++) {
      let entity = entities[i];
      if (!['长春市', '四平市'].includes(entity.name)) {
        entity.polygon.extrudedHeight = 3000;
      }
      entity.polygon.height = 10;
    }
  }

  function handleGeojsonDataReady({ viewer, cesiumObject }) {
    viewer.zoomTo(cesiumObject);
    var entities = cesiumObject.entities.values;
    for (let i = 0; i < entities.length; i++) {
      let entity = entities[i];
      entity.polygon.height = 20000;
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    if (cleanupMapScale) {
      cleanupMapScale();
    }
  });

  // 暴露方法
  defineExpose({
    getViewer: () => cesiumViewer,
    getMapEngine: () => mapEngineRef.value,
    refreshMap: () => {
      console.log('refreshMap');
      if (cesiumViewer) {
        cesiumViewer.resize();
        cesiumViewer.scene.requestRender();
      }
    },
  });
</script>

<style lang="less">
  @prefix-cls: ~'@{namespace}-screen-map';

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .@{prefix-cls} {
    width: 100%;
    height: 100%;
    position: relative;
    color: rgb(17, 255, 255);
    color: rgb(12, 94, 184);

    &-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;

      &-content {
        text-align: center;
        color: #fff;
      }

      &-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(64, 158, 255, 0.3);
        border-top: 3px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px;
      }

      &-text {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    &-controls {
      position: absolute;
      top: 16px;
      right: 16px;
      background: rgba(16, 22, 58, 0.8);
      border: 1px solid rgba(64, 158, 255, 0.3);
      border-radius: 4px;
      padding: 12px;
      backdrop-filter: blur(10px);
      z-index: 100;
    }

    &-scale-info,
    &-resolution-info {
      color: #fff;
      font-size: 42px;
      line-height: 1.5;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
</style>
