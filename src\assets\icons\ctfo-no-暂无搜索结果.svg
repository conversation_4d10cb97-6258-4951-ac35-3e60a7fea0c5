<?xml version="1.0" encoding="UTF-8"?>
<svg width="160px" height="120px" viewBox="0 0 160 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 21</title>
    <defs>
        <linearGradient x1="50%" y1="50%" x2="84.4964268%" y2="51.9993268%" id="linearGradient-1">
            <stop stop-color="#187EE3" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#187EE3" stop-opacity="0.15" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="25.9392659%" x2="96.9521421%" y2="74.0607341%" id="linearGradient-2">
            <stop stop-color="#EBF7FF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#3E9FFF" stop-opacity="0.3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#C5E6FF" offset="0%"></stop>
            <stop stop-color="#93C9FF" offset="100%"></stop>
        </linearGradient>
        <path d="M54.4989003,0.247668233 C63.3335689,0.749222744 72.5049488,1 82.0130401,1 C91.4314766,1 100.850006,0.753929763 110.268627,0.261789288 L110.268631,0.261863933 C112.474719,0.145803259 114.357193,1.84010531 114.473254,4.04619349 C114.483167,4.23462753 114.479734,4.42352688 114.462981,4.61147633 C113.322097,17.3919883 112.751678,30.1725042 112.751678,42.9530201 C112.751678,55.896078 113.336699,68.8391359 114.50674,81.7821938 L114.506744,81.7821935 C114.705615,83.9823629 113.083243,85.9271696 110.883073,86.1260409 C110.832735,86.1305909 110.782315,86.1341864 110.73184,86.1368255 C100.869335,86.6496557 91.3179409,86.9060403 82.0776625,86.9060403 C72.7409234,86.9060403 63.4042713,86.6442748 54.0677062,86.120744 L54.0677118,86.1206449 C52.0205486,86.0049491 50.3923826,84.3600041 50.2976811,82.3117624 C49.6965168,69.192184 49.3959732,56.0726021 49.3959732,42.9530201 C49.3959732,29.9889891 49.6894323,17.0249581 50.2763507,4.06092701 L50.2763238,4.0609258 C50.3759074,1.85403246 52.2456764,0.14572037 54.4525697,0.245303956 C54.4680178,0.246001032 54.4834617,0.246787684 54.4989006,0.247663873 Z" id="path-7"></path>
        <filter x="-1.5%" y="-0.9%" width="102.5%" height="102.0%" filterUnits="objectBoundingBox" id="filter-8">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.9 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.64600756%" x2="50%" y2="100%" id="linearGradient-9">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#B8DCFF" offset="100%"></stop>
        </linearGradient>
        <path d="M57.7298196,5.36912752 L65.7904283,5.36912752 C66.2487037,5.36912752 66.6483298,5.68063547 66.7601842,6.12505069 L67.4607492,8.90850636 C67.5726036,9.35292157 67.9722298,9.66442953 68.4305051,9.66442953 L96.9741601,9.66442953 C97.4330278,9.66442953 97.8330109,9.35213216 97.9443026,8.90696516 L98.639396,6.12659189 C98.7506877,5.68142489 99.1506708,5.36912752 99.6095385,5.36912752 L107.417216,5.36912752 C108.521785,5.36919872 109.417158,6.26468693 109.417087,7.36925643 C109.417085,7.40454562 109.416148,7.43982857 109.414229,7.47506558 C108.77566,19.4077793 108.456376,31.2721195 108.456376,43.0680862 C108.456376,54.8549052 108.775165,66.6416718 109.412744,78.4283859 C109.472654,79.5313295 108.627131,80.4740292 107.524189,80.5339661 C107.488326,80.535915 107.452417,80.5368975 107.416501,80.5369128 L57.7297871,80.5369128 C56.6678341,80.5365701 55.7913047,79.7063265 55.7333816,78.6459543 C55.087861,66.7728286 54.7651007,54.8839342 54.7651007,42.9792713 C54.7651007,31.0746084 55.087861,19.1699334 55.7333816,7.26524632 C55.7883116,6.20267393 56.6658285,5.36940288 57.7298196,5.36912752 Z" id="path-10"></path>
        <filter x="-2.7%" y="-0.7%" width="103.4%" height="102.7%" filterUnits="objectBoundingBox" id="filter-11">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.454382622   0 0 0 0 0.728604827   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-3.7%" y="-1.3%" width="105.3%" height="104.0%" filterUnits="objectBoundingBox" id="filter-12">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.90034965 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <rect id="path-13" x="62.2818792" y="23.6241611" width="26" height="6.44295302" rx="1"></rect>
        <filter x="-5.8%" y="-7.8%" width="107.7%" height="131.0%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.454901961   0 0 0 0 0.729411765   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <rect id="path-15" x="62.2818792" y="39.7315436" width="26" height="6.44295302" rx="1"></rect>
        <filter x="-5.8%" y="-7.8%" width="107.7%" height="131.0%" filterUnits="objectBoundingBox" id="filter-16">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.454901961   0 0 0 0 0.729411765   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <rect id="path-17" x="62.2818792" y="55.8389262" width="26" height="6.44295302" rx="1"></rect>
        <filter x="-5.8%" y="-7.8%" width="107.7%" height="131.0%" filterUnits="objectBoundingBox" id="filter-18">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.454901961   0 0 0 0 0.729411765   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <rect id="path-19" x="92.3489933" y="23.6241611" width="6.44295302" height="6.44295302" rx="1"></rect>
        <filter x="-23.3%" y="-7.8%" width="131.0%" height="131.0%" filterUnits="objectBoundingBox" id="filter-20">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.454901961   0 0 0 0 0.729411765   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <rect id="path-21" x="92.3489933" y="39.6241611" width="6.44295302" height="6.44295302" rx="1"></rect>
        <filter x="-23.3%" y="-7.8%" width="131.0%" height="131.0%" filterUnits="objectBoundingBox" id="filter-22">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.454901961   0 0 0 0 0.729411765   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <rect id="path-23" x="92.3489933" y="55.6241611" width="6.44295302" height="6.44295302" rx="1"></rect>
        <filter x="-23.3%" y="-7.8%" width="131.0%" height="131.0%" filterUnits="objectBoundingBox" id="filter-24">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.454901961   0 0 0 0 0.729411765   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="78.7718341%" y1="44.8843782%" x2="35.0456402%" y2="78.3024658%" id="linearGradient-25">
            <stop stop-color="#74BAFE" offset="0%"></stop>
            <stop stop-color="#AFD6FC" offset="48.5705661%"></stop>
            <stop stop-color="#60A4E9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="66.4617756%" y1="33.5939678%" x2="45.8764048%" y2="81.9546753%" id="linearGradient-26">
            <stop stop-color="#D8ECFF" offset="0%"></stop>
            <stop stop-color="#76B4F1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="21.4800755%" y1="14.3028903%" x2="67.8769559%" y2="100%" id="linearGradient-27">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#7ABBFA" offset="100%"></stop>
        </linearGradient>
        <circle id="path-28" cx="22" cy="22" r="22"></circle>
        <filter x="-3.4%" y="-1.1%" width="104.5%" height="104.5%" filterUnits="objectBoundingBox" id="filter-29">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.454382622   0 0 0 0 0.728604827   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-3.4%" y="-1.1%" width="104.5%" height="104.5%" filterUnits="objectBoundingBox" id="filter-30">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.90034965 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="45.1386072%" y1="28.2721395%" x2="72.8724215%" y2="92.8737455%" id="linearGradient-31">
            <stop stop-color="#9ECFFF" offset="0%"></stop>
            <stop stop-color="#0B86FF" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="45.1386072%" cy="28.2721395%" fx="45.1386072%" fy="28.2721395%" r="70.3031433%" id="radialGradient-32">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#CBE5FF" offset="100%"></stop>
        </radialGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图标" transform="translate(-730.000000, -6150.000000)">
            <g id="编组-9" transform="translate(60.000000, 5991.000000)">
                <g id="编组-21" transform="translate(670.000000, 159.000000)">
                    <rect id="矩形" stroke="#FFFFFF" opacity="0" x="0.5" y="0.5" width="159" height="119"></rect>
                    <g id="编组-31" transform="translate(0.000000, 12.000000)">
                        <ellipse id="椭圆形" fill="url(#linearGradient-1)" cx="80" cy="77.6107383" rx="80" ry="19.3288591"></ellipse>
                        <polygon id="路径-13" fill="url(#linearGradient-2)" points="46.5281499 84.5782284 23 69.0399988 54.1061563 63"></polygon>
                        <path d="M21.6784051,61.4481376 C25.7861384,65.6249156 27.840005,69.6286491 27.840005,73.4593379 C27.840005,77.0081521 26.0772781,79.6857405 22.5518244,81.4921031 L22.5503356,88.3489933 L20.4026846,88.3489933 L20.4024597,81.7393727 C16.8029014,81.01509 15.0031223,78.2550784 15.0031223,73.4593379 C15.0031223,68.1269678 17.2282166,64.1232344 21.6784051,61.4481376 Z" id="形状结合" fill="url(#linearGradient-3)"></path>
                        <path d="M131.165636,48.5622315 C134.586133,52.0402214 136.296381,55.3741176 136.296381,58.5639201 C136.296381,61.5189752 134.828595,63.7485787 131.893023,65.2527305 L131.89169,70.9624891 L130.103348,70.9624891 L130.103315,65.4587101 C127.105871,64.8556501 125.607149,62.5573867 125.607149,58.5639201 C125.607149,54.1236724 127.459978,50.7897762 131.165636,48.5622315 Z" id="形状结合" fill="url(#linearGradient-4)"></path>
                        <path d="M33.4045449,44.2669295 C36.1378046,47.0461313 37.5044345,49.7101902 37.5044345,52.2591063 C37.5044345,54.620697 36.3312966,56.4024655 33.9850208,57.6044119 L33.9847219,62.166589 L32.5556885,62.166589 L32.5544517,57.7683704 C30.1600531,57.2861003 28.9628538,55.4496789 28.9628538,52.2591063 C28.9628538,48.710981 30.4434175,46.0469221 33.4045449,44.2669295 Z" id="形状结合" fill="url(#linearGradient-5)"></path>
                        <path d="M50.0356868,0.347064193 C58.8979822,0.782354731 69.5737972,1 82.0631315,1 C92.4336469,1 99.8065951,0.849937795 104.181976,0.549813386 C106.385914,0.398314053 108.295408,2.06210489 108.446953,4.26603984 C108.453179,4.35658217 108.456321,4.44731017 108.456376,4.53806628 L108.456376,42.9530201 L108.456376,42.9530201 L108.456376,81.9060403 C108.456376,84.1151793 106.665515,85.9060403 104.456376,85.9060403 L49.7505505,85.9060403 C46.5380092,85.9057409 43.8957055,83.3751417 43.7568053,80.1656045 C43.2209485,67.8762206 42.9530201,55.5180192 42.9530201,43.0910004 C42.9530201,30.7551568 43.2170314,18.4192769 43.745054,6.08336049 L43.7450494,6.08336029 C43.8866991,2.7726807 46.685365,0.203674387 49.9960446,0.345324098 C50.0092592,0.345889493 50.0224719,0.346498584 50.0356825,0.347151359 Z" id="矩形" fill="#B8DFFF"></path>
                        <path d="M51.1158625,0.363530274 C59.5107806,0.787843425 69.8160692,1 82.0317285,1 C92.8231154,1 100.5619,0.834431697 105.248083,0.50329509 C107.451721,0.347497987 109.364431,2.0075909 109.52024,4.21122848 C109.526821,4.30429985 109.530143,4.39757297 109.530201,4.49087668 L109.530201,42.9530201 L109.530201,42.9530201 L109.530201,81.9060403 C109.530201,84.1151793 107.73934,85.9060403 105.530201,85.9060403 L50.824376,85.9060403 C47.6118347,85.9057409 44.969531,83.3751417 44.8306308,80.1656045 C44.294774,67.8762206 44.0268456,55.5180192 44.0268456,43.0910004 C44.0268456,30.760417 44.2906318,18.4297971 44.8182042,6.09914092 L44.8182154,6.0991414 C44.9600087,2.78846795 47.7587859,0.219582999 51.0694594,0.361376265 C51.0849296,0.362038842 51.1003972,0.362761302 51.1158618,0.36354363 Z" id="矩形" fill="#3E9FFF"></path>
                        <g id="矩形">
                            <use fill="url(#linearGradient-6)" fill-rule="evenodd" xlink:href="#path-7"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                        </g>
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-11)" xlink:href="#path-10"></use>
                            <use fill="url(#linearGradient-9)" fill-rule="evenodd" xlink:href="#path-10"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-12)" xlink:href="#path-10"></use>
                        </g>
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-13"></use>
                        </g>
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-16)" xlink:href="#path-15"></use>
                            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-15"></use>
                        </g>
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-18)" xlink:href="#path-17"></use>
                            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-17"></use>
                        </g>
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-20)" xlink:href="#path-19"></use>
                            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-19"></use>
                        </g>
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-22)" xlink:href="#path-21"></use>
                            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-21"></use>
                        </g>
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-24)" xlink:href="#path-23"></use>
                            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-23"></use>
                        </g>
                        <polyline id="路径-7" stroke="#FF9C4E" stroke-linecap="round" points="93.5421602 26.8456376 94.9970676 28.3005449 97.721582 25.5760305"></polyline>
                        <polyline id="路径-7" stroke="#FF9C4E" stroke-linecap="round" points="93.5421602 42.8456376 94.9970676 44.3005449 97.721582 41.5760305"></polyline>
                        <polyline id="路径-7" stroke="#FF9C4E" stroke-linecap="round" points="93.5421602 58.8456376 94.9970676 60.3005449 97.721582 57.5760305"></polyline>
                        <g id="编组-33" transform="translate(80.000000, 19.000000)">
                            <polygon id="路径-52" fill="url(#linearGradient-25)" points="30.9017614 41.7804537 34.2105282 47.4080048 38.7522113 44.5775924 35.4183998 38.7904705"></polygon>
                            <path d="M33.0460456,47.8724793 L39.1279103,43.7915328 C40.0451281,43.1760773 41.2876052,43.420704 41.9030606,44.3379218 C41.9041052,44.3394786 41.9051476,44.3410368 41.9061878,44.3425966 L51.7176206,59.0538939 C52.3160648,59.9512033 52.0952095,61.161415 51.2183915,61.7894992 L45.1708124,66.121514 C44.2728529,66.7647423 43.0234734,66.5582418 42.3802452,65.6602823 C42.3648728,65.6388222 42.3499267,65.6170597 42.3354159,65.5950079 L32.4896975,50.6326422 C31.8863787,49.7157891 32.1346578,48.4840228 33.0460456,47.8724793 Z" id="路径-53" fill="url(#linearGradient-26)"></path>
                            <g id="椭圆形">
                                <use fill="black" fill-opacity="1" filter="url(#filter-29)" xlink:href="#path-28"></use>
                                <use fill="url(#linearGradient-27)" fill-rule="evenodd" xlink:href="#path-28"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-30)" xlink:href="#path-28"></use>
                            </g>
                            <circle id="椭圆形" fill="url(#linearGradient-31)" cx="22" cy="22" r="14"></circle>
                            <circle id="椭圆形" fill="url(#radialGradient-32)" cx="22" cy="22" r="13"></circle>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>