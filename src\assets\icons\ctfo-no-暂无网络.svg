<?xml version="1.0" encoding="UTF-8"?>
<svg width="160px" height="120px" viewBox="0 0 160 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 22</title>
    <defs>
        <linearGradient x1="50%" y1="50%" x2="84.4964268%" y2="51.9993268%" id="linearGradient-1">
            <stop stop-color="#187EE3" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#187EE3" stop-opacity="0.15" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="39.7192828%" x2="96.9521421%" y2="60.2807172%" id="linearGradient-2">
            <stop stop-color="#EBF7FF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#3E9FFF" stop-opacity="0.3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#93C9FF" offset="0%"></stop>
            <stop stop-color="#C5E6FF" offset="100%"></stop>
        </linearGradient>
        <path d="M64.6238947,88.2067661 L4.33293233,27.2979731 C21.6330357,9.38248903 41.3089179,0.285358009 63.3605789,0.00658008455 C85.4122399,-0.27219784 106.686018,8.30724604 127.181912,25.7449117 L64.6238947,88.2067661 Z" id="path-7"></path>
        <filter x="-1.2%" y="-1.7%" width="102.4%" height="103.4%" filterUnits="objectBoundingBox" id="filter-8">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.873266006   0 0 0 0 0.936633003   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.64600756%" x2="50%" y2="100%" id="linearGradient-9">
            <stop stop-color="#B8DCFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <path d="M64.6238947,88.2067661 L22.6296201,45.7821559 C33.2901966,32.9828741 46.8671829,26.4789781 63.3605789,26.2704679 C79.853975,26.0619578 95.8102701,31.196081 111.229464,41.6728376 L64.6238947,88.2067661 Z" id="path-10"></path>
        <filter x="-2.3%" y="-3.2%" width="104.5%" height="106.5%" filterUnits="objectBoundingBox" id="filter-11">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.90034965 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.64600756%" x2="50%" y2="100%" id="linearGradient-12">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#B8DCFF" offset="100%"></stop>
        </linearGradient>
        <path d="M64.6238947,88.2067661 L39.8461548,63.1751167 C47.4974658,56.1755011 56.1345549,52.614867 65.7574222,52.4932143 C75.3802894,52.3715617 84.0769553,55.2155198 91.8474196,61.0250887 L64.6238947,88.2067661 Z" id="path-13"></path>
        <filter x="-2.9%" y="-4.2%" width="105.8%" height="108.4%" filterUnits="objectBoundingBox" id="filter-14">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.90034965 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M76.696332,32.4388369 C76.696332,36.8629753 74.1856755,40.7025555 70.5101081,42.6034211 C69.5176207,43.1164865 68.7101734,43.8860848 68.1466425,44.8070794 C67.4863861,45.8920867 67.170977,47.1873667 67.3055515,48.5204959 C67.3476061,48.9158086 67.0364024,49.2606559 66.6368842,49.2606559 L60.6441112,49.2606559 C59.8492803,49.2606559 59.2016402,48.6172214 59.2016402,47.818185 C59.2016402,42.4604356 62.2001295,37.5989299 66.9060333,35.037808 C67.9279588,34.4826879 68.6260643,33.6205697 68.6176534,32.2958515 C68.6092425,30.5085332 67.1289224,29.0744731 65.3416042,29.0744731 L63.2388768,29.0744731 C61.8973367,29.0744731 60.7366312,29.8650986 60.198333,31.0005714 C59.9747642,31.4687826 59.5018056,31.7666319 58.9829566,31.7659642 L53.3476472,31.7659642 C52.4939399,31.7659642 51.8589162,30.9837496 52.0271344,30.1510695 C52.1028326,29.785195 52.1911472,29.4277313 52.300489,29.0744731 C53.7387545,24.3980074 58.0914002,21 63.2388768,21 L65.2574951,21 C68.4157916,21 71.2755008,22.2784582 73.3445846,24.3517474 C75.4178738,26.4208312 76.696332,29.2805404 76.696332,32.4388369 Z M68.6218589,55.9893835 C68.6218589,57.4739091 68.0204788,58.8196546 67.0448133,59.7953201 C66.0691478,60.7709856 64.7234023,61.3723656 63.2388768,61.3723656 C61.7543513,61.3723656 60.4086057,60.7709856 59.4329402,59.7953201 C58.4572747,58.8196546 57.8558947,57.4739091 57.8558947,55.9893835 C57.8558947,53.016127 60.2656203,50.6064015 63.2388768,50.6064015 C64.7234023,50.6064015 66.0691478,51.2077815 67.0448133,52.183447 C68.0204788,53.1591125 68.6218589,54.504858 68.6218589,55.9893835 Z" id="path-15"></path>
        <filter x="-2.0%" y="-1.2%" width="104.0%" height="105.0%" filterUnits="objectBoundingBox" id="filter-16">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-4.0%" y="-2.5%" width="108.1%" height="107.4%" filterUnits="objectBoundingBox" id="filter-17">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图标" transform="translate(-1050.000000, -6150.000000)">
            <g id="编组-9" transform="translate(60.000000, 5991.000000)">
                <g id="编组-22" transform="translate(990.000000, 159.000000)">
                    <rect id="矩形" stroke="#FFFFFF" opacity="0" x="0.5" y="0.5" width="159" height="119"></rect>
                    <g id="编组-31" transform="translate(0.000000, 9.000000)">
                        <ellipse id="椭圆形" fill="url(#linearGradient-1)" cx="80" cy="81.6107383" rx="80" ry="19.3288591"></ellipse>
                        <polygon id="路径-13" fill="url(#linearGradient-2)" points="77.2062732 88.2067661 23.0853374 73.3127708 65.42335 63.6657805"></polygon>
                        <path d="M21.6784051,65.4481376 C25.7861384,69.6249156 27.840005,73.6286491 27.840005,77.4593379 C27.840005,81.0081521 26.0772781,83.6857405 22.5518244,85.4921031 L22.5503356,92.3489933 L20.4026846,92.3489933 L20.4024597,85.7393727 C16.8029014,85.01509 15.0031223,82.2550784 15.0031223,77.4593379 C15.0031223,72.1269678 17.2282166,68.1232344 21.6784051,65.4481376 Z" id="形状结合" fill="url(#linearGradient-3)"></path>
                        <path d="M131.165636,52.5622315 C134.586133,56.0402214 136.296381,59.3741176 136.296381,62.5639201 C136.296381,65.5189752 134.828595,67.7485787 131.893023,69.2527305 L131.89169,74.9624891 L130.103348,74.9624891 L130.103315,69.4587101 C127.105871,68.8556501 125.607149,66.5573867 125.607149,62.5639201 C125.607149,58.1236724 127.459978,54.7897762 131.165636,52.5622315 Z" id="形状结合" fill="url(#linearGradient-4)"></path>
                        <path d="M33.4045449,48.2669295 C36.1378046,51.0461313 37.5044345,53.7101902 37.5044345,56.2591063 C37.5044345,58.620697 36.3312966,60.4024655 33.9850208,61.6044119 L33.9847219,66.166589 L32.5556885,66.166589 L32.5544517,61.7683704 C30.1600531,61.2861003 28.9628538,59.4496789 28.9628538,56.2591063 C28.9628538,52.710981 30.4434175,50.0469221 33.4045449,48.2669295 Z" id="形状结合" fill="url(#linearGradient-5)"></path>
                        <g id="编组-34" transform="translate(22.000000, 0.000000)">
                            <polygon id="路径-55" fill="#B8DFFF" points="4.1717685 27.3563764 0 32.4496768 55.2062732 88.2067661 62.4669072 88.2067661"></polygon>
                            <polygon id="路径-55" fill="#3E9FFF" points="5.2571059 26.271039 1.0853374 31.3643394 57.3632607 88.2067661 64.6238947 88.2067661"></polygon>
                            <g id="路径-54">
                                <use fill="url(#linearGradient-6)" fill-rule="evenodd" xlink:href="#path-7"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                            </g>
                            <g id="路径-54">
                                <use fill="url(#linearGradient-9)" fill-rule="evenodd" xlink:href="#path-10"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-11)" xlink:href="#path-10"></use>
                            </g>
                            <g id="路径-54">
                                <use fill="url(#linearGradient-12)" fill-rule="evenodd" xlink:href="#path-13"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                            </g>
                            <g id="形状">
                                <use fill="black" fill-opacity="1" filter="url(#filter-16)" xlink:href="#path-15"></use>
                                <use fill="#FF9E4F" fill-rule="evenodd" xlink:href="#path-15"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-15"></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>