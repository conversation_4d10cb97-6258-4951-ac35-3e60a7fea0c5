/*
 * @Description: 下拉列表mock接口
 * @Author: XIAOLIJUN
 * @Date: 2022-07-07 19:41:02
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2022-07-12 14:18:28
 */
import { defHttp } from '/@/utils/http/axios';
import { DemoOptionsItem, selectParams } from './model/optionsModel';
enum Api {
  OPTIONS_LIST = '/mock/select/getDemoOptions',
}

/**
 * @description: Get sample options value
 */
export const optionsListApi = (params?: selectParams) =>
  defHttp.get<DemoOptionsItem[]>({ url: Api.OPTIONS_LIST, params });
