<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>大屏缩放测试</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', sans-serif;
      background: #0a0e27;
      overflow: hidden;
    }

    .screen-container {
      height: 100vh;
      width: 100vw;
      position: relative;
      background: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
                  radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.15) 0%, transparent 50%);
    }

    .design-container {
      width: 8600px;
      height: 3600px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform-origin: center center;
      background: transparent;
      border: 2px solid rgba(64, 158, 255, 0.3);
      box-shadow: 0 0 20px rgba(64, 158, 255, 0.1);
    }

    .header {
      height: 120px;
      background: linear-gradient(90deg, rgba(64, 158, 255, 0.1) 0%, rgba(64, 158, 255, 0.05) 100%);
      border-bottom: 2px solid rgba(64, 158, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      color: #fff;
      font-size: 48px;
      font-weight: bold;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, transparent 0%, #409eff 50%, transparent 100%);
    }

    .body {
      height: calc(100% - 120px);
      display: flex;
    }

    .sidebar {
      width: 480px;
      background: rgba(16, 22, 58, 0.8);
      border: 2px solid rgba(64, 158, 255, 0.1);
      backdrop-filter: blur(10px);
      position: relative;
      padding: 20px;
    }

    .sidebar::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(180deg, rgba(64, 158, 255, 0.05) 0%, transparent 100%);
      pointer-events: none;
    }

    .center {
      flex: 1;
      background: rgba(10, 14, 39, 0.9);
      border: 2px solid rgba(64, 158, 255, 0.1);
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 36px;
    }

    .map-placeholder {
      width: 80%;
      height: 80%;
      border: 3px dashed rgba(64, 158, 255, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(255, 255, 255, 0.6);
      font-size: 32px;
    }

    .info-panel {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(16, 22, 58, 0.9);
      border: 1px solid rgba(64, 158, 255, 0.3);
      border-radius: 8px;
      padding: 16px;
      color: #fff;
      font-size: 16px;
      backdrop-filter: blur(10px);
      z-index: 1000;
    }

    .info-item {
      margin-bottom: 8px;
    }

    .info-item:last-child {
      margin-bottom: 0;
    }

    .sidebar-content {
      color: #fff;
      font-size: 18px;
      line-height: 1.6;
    }

    .sidebar-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 20px;
      color: #409eff;
    }
  </style>
</head>
<body>
  <div class="screen-container">
    <div class="design-container" id="designContainer">
      <div class="header">
        云控中心大屏 - 缩放测试
      </div>
      <div class="body">
        <div class="sidebar">
          <div class="sidebar-title">左侧面板</div>
          <div class="sidebar-content">
            <p>这是一个 8600×3600 的设计稿</p>
            <p>支持响应式缩放适配</p>
            <p>保持比例不变形</p>
            <p>居中显示</p>
          </div>
        </div>
        <div class="center">
          <div class="map-placeholder">
            地图区域
            <br>
            (支持地图缩放适配)
          </div>
        </div>
        <div class="sidebar">
          <div class="sidebar-title">右侧面板</div>
          <div class="sidebar-content">
            <p>试着调整浏览器窗口大小</p>
            <p>观察整体缩放效果</p>
            <p>内容始终居中显示</p>
            <p>比例保持不变</p>
          </div>
        </div>
      </div>
    </div>

    <div class="info-panel">
      <div class="info-item">设计稿: <span id="designSize">8600 × 3600</span></div>
      <div class="info-item">屏幕: <span id="screenSize">-</span></div>
      <div class="info-item">缩放: <span id="scaleValue">-</span></div>
      <div class="info-item">容器: <span id="containerSize">-</span></div>
    </div>
  </div>

  <script>
    // 设计稿尺寸
    const DESIGN_WIDTH = 8600;
    const DESIGN_HEIGHT = 3600;

    // DOM 元素
    const designContainer = document.getElementById('designContainer');
    const screenSizeEl = document.getElementById('screenSize');
    const scaleValueEl = document.getElementById('scaleValue');
    const containerSizeEl = document.getElementById('containerSize');

    // 计算缩放比例
    function calculateScale() {
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;
      const scaleX = screenWidth / DESIGN_WIDTH;
      const scaleY = screenHeight / DESIGN_HEIGHT;
      return Math.min(scaleX, scaleY);
    }

    // 更新缩放
    function updateScale() {
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;
      const scale = calculateScale();

      // 应用缩放
      designContainer.style.transform = `scale(${scale})`;
      designContainer.style.marginLeft = `${-DESIGN_WIDTH / 2}px`;
      designContainer.style.marginTop = `${-DESIGN_HEIGHT / 2}px`;

      // 更新信息显示
      screenSizeEl.textContent = `${screenWidth} × ${screenHeight}`;
      scaleValueEl.textContent = `${(scale * 100).toFixed(1)}%`;
      containerSizeEl.textContent = `${Math.round(DESIGN_WIDTH * scale)} × ${Math.round(DESIGN_HEIGHT * scale)}`;
    }

    // 防抖处理
    let resizeTimer = null;
    function handleResize() {
      if (resizeTimer) {
        clearTimeout(resizeTimer);
      }
      resizeTimer = setTimeout(() => {
        updateScale();
        resizeTimer = null;
      }, 100);
    }

    // 初始化
    updateScale();
    window.addEventListener('resize', handleResize);

    // 模拟地图缩放回调
    window.addEventListener('resize', () => {
      console.log('地图需要重新渲染，当前缩放比例:', calculateScale());
    });
  </script>
</body>
</html>
