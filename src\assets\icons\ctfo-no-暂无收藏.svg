<?xml version="1.0" encoding="UTF-8"?>
<svg width="160px" height="120px" viewBox="0 0 160 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 20</title>
    <defs>
        <linearGradient x1="50%" y1="50%" x2="84.4964268%" y2="51.9993268%" id="linearGradient-1">
            <stop stop-color="#187EE3" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#187EE3" stop-opacity="0.15" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="25.9392659%" x2="81.6701196%" y2="59.8504648%" id="linearGradient-2">
            <stop stop-color="#EBF7FF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#3E9FFF" stop-opacity="0.3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FFC764" offset="0%"></stop>
            <stop stop-color="#FF9C4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#C5E6FF" offset="0%"></stop>
            <stop stop-color="#93C9FF" offset="100%"></stop>
        </linearGradient>
        <path d="M47.2483221,10.7020154 C51.4778952,25.4210738 53.5926818,38.5106602 53.5926818,49.9707744 C53.5926818,61.4308887 52.8792567,73.8343409 51.4524066,87.181131 L79.4420467,85.3490369 C79.3977633,80.0546042 80.3532019,77.4073878 82.3083625,77.4073878 C85.2411034,77.4073878 93.9284315,82.4472848 99.3247475,81.8725379 C102.922292,81.4893733 109.797316,80.3791411 119.94982,78.5418411 C120.359588,62.5410968 120.188873,49.7478941 119.437675,40.162233 C118.381612,26.6863465 115.957975,13.3624546 112.166764,0.190557362 L88,3.37028465 C87.1439568,10.5844276 84.6499641,14.1914991 80.5180219,14.1914991 C76.3860798,14.1914991 73.0769189,11.6081066 70.5905393,6.44132147 L47.2483221,10.7020154 Z" id="path-7"></path>
        <filter x="-1.4%" y="-1.1%" width="102.7%" height="102.3%" filterUnits="objectBoundingBox" id="filter-8">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.9 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.64600756%" x2="50%" y2="100%" id="linearGradient-9">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#B8DCFF" offset="100%"></stop>
        </linearGradient>
        <path d="M52.5826664,13.0179106 C56.3751083,26.2157378 58.2713293,37.9525007 58.2713293,48.2281992 C58.2713293,58.5038978 57.5762362,70.1277206 56.18605,83.0996676 L78,82.2758401 C79.2155537,76.7067656 80.6998769,73.9222283 82.4529694,73.9222283 C85.0826082,73.9222283 92.8720912,80.0314686 97.7106919,79.5161225 C100.936426,79.1725584 106.932197,78.1509904 115.698005,76.4514185 C116.525705,62.2479964 116.525705,50.1333623 115.698005,40.107516 C114.870306,30.0816697 112.698378,18.2333099 109.182224,4.56243649 L89.5097096,7.49442165 C88.091633,13.1014766 85.7393863,16.1789992 82.4529694,16.7269897 C79.1665526,17.2749801 75.3214566,15.2666553 70.9176814,10.7020154 L52.5826664,13.0179106 Z" id="path-10"></path>
        <filter x="-2.4%" y="-0.6%" width="103.1%" height="102.5%" filterUnits="objectBoundingBox" id="filter-11">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.454382622   0 0 0 0 0.728604827   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <path d="M87.8466056,50.3048617 L79.2095417,54.8456349 C78.7206971,55.1026358 78.1160699,54.9146892 77.8590691,54.4258446 C77.7567301,54.2311842 77.7214154,54.0082161 77.7585922,53.7914585 L79.4081247,44.1739465 C79.5194005,43.5251583 79.3043036,42.8631579 78.8329314,42.4036836 L71.8453999,35.5925238 C71.4499163,35.2070226 71.441824,34.5739093 71.8273252,34.1784258 C71.9808339,34.0209421 72.1819763,33.918455 72.3996133,33.8868304 L83.0966787,32.3324559 L83.0966787,32.3324559 L87.8805518,22.6392703 C88.124974,22.1440166 88.7245999,21.9406776 89.2198535,22.1850999 C89.4170661,22.2824302 89.5766937,22.4420577 89.6740239,22.6392703 L93.9925559,31.3895718 C94.2838799,31.9798587 94.8470111,32.3889974 95.4984314,32.4836544 L105.154962,33.8868304 C105.701507,33.9662481 106.080189,34.4736909 106.000771,35.0202358 C105.969147,35.2378728 105.866659,35.4390152 105.709176,35.5925238 L98.7216443,42.4036836 C98.2502721,42.8631579 98.0351751,43.5251583 98.146451,44.1739465 L99.7959834,53.7914585 C99.8893444,54.3357949 99.5237566,54.8527508 98.9794202,54.9461118 C98.7626625,54.9832887 98.5396945,54.947974 98.345034,54.8456349 L89.7079701,50.3048617 C89.125322,49.9985455 88.4292536,49.9985455 87.8466056,50.3048617 Z" id="path-12"></path>
        <filter x="-1.0%" y="2.2%" width="99.2%" height="99.8%" filterUnits="objectBoundingBox" id="filter-13">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-2.3%" y="0.7%" width="101.9%" height="102.6%" filterUnits="objectBoundingBox" id="filter-14">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图标" transform="translate(-410.000000, -6150.000000)">
            <g id="编组-9" transform="translate(60.000000, 5991.000000)">
                <g id="编组-20" transform="translate(350.000000, 159.000000)">
                    <rect id="矩形" stroke="#FFFFFF" opacity="0" x="0.5" y="0.5" width="159" height="119"></rect>
                    <g id="编组-32" transform="translate(0.000000, 10.892484)">
                        <ellipse id="椭圆形" fill="url(#linearGradient-1)" cx="80" cy="79.0996676" rx="80" ry="19.3288591"></ellipse>
                        <polygon id="路径-13" fill="url(#linearGradient-2)" points="45.00466 85.3490369 21.4765101 69.8108074 52.5826664 63.7708085"></polygon>
                        <path d="M22.7522306,62.9370669 C26.8599639,67.113845 28.9138305,71.1175784 28.9138305,74.9482672 C28.9138305,78.4970814 27.1511036,81.1746698 23.6256499,82.9810324 L23.6241611,89.8379226 L21.4765101,89.8379226 L21.4762852,83.228302 C17.8767269,82.5040194 16.0769478,79.7440077 16.0769478,74.9482672 C16.0769478,69.6158972 18.3020421,65.6121637 22.7522306,62.9370669 Z" id="形状结合" fill="url(#linearGradient-3)"></path>
                        <path d="M132.239462,50.0511609 C135.659958,53.5291508 137.370206,56.8630469 137.370206,60.0528494 C137.370206,63.0079045 135.90242,65.237508 132.966848,66.7416598 L132.965515,72.4514185 L131.177173,72.4514185 L131.17714,66.9476394 C128.179697,66.3445794 126.680975,64.0463161 126.680975,60.0528494 C126.680975,55.6126017 128.533804,52.2787055 132.239462,50.0511609 Z" id="形状结合" fill="url(#linearGradient-4)"></path>
                        <path d="M34.4783704,45.7558589 C37.2116301,48.5350606 38.57826,51.1991195 38.57826,53.7480357 C38.57826,56.1096263 37.4051221,57.8913949 35.0588463,59.0933413 L35.0585474,63.6555184 L33.629514,63.6555184 L33.6282772,59.2572997 C31.2338786,58.7750296 30.0366793,56.9386083 30.0366793,53.7480357 C30.0366793,50.1999103 31.517243,47.5358514 34.4783704,45.7558589 Z" id="形状结合" fill="url(#linearGradient-5)"></path>
                        <path d="M39.2483221,10.7020154 C43.4778952,25.4210738 45.5926818,38.5106602 45.5926818,49.9707744 C45.5926818,61.4308887 45.0633412,73.2236428 44.00466,85.3490369 L50.4990294,87.181131 L50.7843622,86.7012094 L71.4420467,85.3490369 C71.3977633,80.0546042 72.3532019,77.4073878 74.3083625,77.4073878 C77.2411034,77.4073878 85.9284315,82.4472848 91.3247475,81.8725379 C94.9222916,81.4893733 101.797316,80.3791411 111.94982,78.5418411 C112.359588,62.5410968 112.188873,49.7478941 111.437675,40.162233 C110.531356,28.597144 107.878164,17.1440017 103.478101,5.80280616 C103.298579,5.34008652 104.857242,3.46933692 108.154092,0.190557362 L101.154092,0.190557362 L80,3.37028465 C79.1439568,10.5844276 76.6499641,14.1914991 72.5180219,14.1914991 C70.4074651,14.1914991 69.4316376,11.6081066 69.5905393,6.44132147 L62.5905393,6.44132147 L39.2483221,10.7020154 Z" id="路径-12" fill="#B8DFFF"></path>
                        <path d="M40.2483221,10.7020154 C44.4778952,25.4210738 46.5926818,38.5106602 46.5926818,49.9707744 C46.5926818,61.4308887 46.0633412,73.2236428 45.00466,85.3490369 L51.4990294,87.181131 L51.7843622,86.7012094 L72.4420467,85.3490369 C72.3977633,80.0546042 73.3532019,77.4073878 75.3083625,77.4073878 C78.2411034,77.4073878 86.9284315,82.4472848 92.3247475,81.8725379 C95.9222916,81.4893733 102.797316,80.3791411 112.94982,78.5418411 C113.359588,62.5410968 113.188873,49.7478941 112.437675,40.162233 C111.531356,28.597144 108.878164,17.1440017 104.478101,5.80280616 C104.298579,5.34008652 106.861466,3.46933692 112.166764,0.190557362 L104.478101,0 L81,3.37028465 C80.1439568,10.5844276 77.6499641,14.1914991 73.5180219,14.1914991 C71.4074651,14.1914991 70.4316376,11.6081066 70.5905393,6.44132147 L63.5905393,6.44132147 L40.2483221,10.7020154 Z" id="路径-12" fill="#3E9FFF"></path>
                        <g id="路径-12">
                            <use fill="url(#linearGradient-6)" fill-rule="evenodd" xlink:href="#path-7"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                        </g>
                        <g id="路径-12">
                            <use fill="black" fill-opacity="1" filter="url(#filter-11)" xlink:href="#path-10"></use>
                            <use fill="url(#linearGradient-9)" fill-rule="evenodd" xlink:href="#path-10"></use>
                        </g>
                        <path d="M45.00466,66.2393514 L53.1708264,68.8100325 L119.437675,60.5707649" id="路径-51" stroke="#2E86DD" stroke-dasharray="3"></path>
                        <path d="M45.00466,67.2393514 L53.1708264,69.8100325 L119.437675,61.5707649" id="路径-51" stroke="#DCEEFF" stroke-dasharray="3"></path>
                        <g id="星形" transform="translate(88.777288, 38.305402) rotate(-6.000000) translate(-88.777288, -38.305402) ">
                            <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                            <use fill="#FF9E4F" fill-rule="evenodd" xlink:href="#path-12"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-12"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>