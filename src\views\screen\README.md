# 大屏缩放适配方案

## 概述

本方案实现了一个响应式的大屏缩放适配系统，设计稿尺寸为 8600×3600，支持浏览器窗口大小变化时的整体比例缩放，并特别考虑了地图组件的缩放适配问题。

## 核心特性

- ✅ **固定设计稿尺寸**: 8600×3600 像素
- ✅ **响应式缩放**: 根据浏览器窗口大小自动计算最佳缩放比例
- ✅ **居中显示**: 缩放后的内容始终在屏幕中央
- ✅ **地图适配**: 专门处理 Cesium 等地图组件的缩放问题
- ✅ **性能优化**: 防抖处理、渲染质量自适应
- ✅ **TypeScript 支持**: 完整的类型定义

## 文件结构

```
src/views/screen/
├── index.vue                    # 主屏幕组件
├── components/
│   └── ScreenMap.vue           # 地图组件示例
├── hooks/
│   └── useScreenScale.ts       # 缩放适配 Hook
└── README.md                   # 使用文档
```

## 使用方法

### 1. 基础使用

```vue
<template>
  <div :class="prefixCls" ref="screenRef">
    <div :class="`${prefixCls}-container`" :style="containerStyle" ref="containerRef">
      <!-- 你的大屏内容 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { useScreenScale } from '/@/hooks/web/useScreenScale';

const {
  scale,
  screenWidth,
  screenHeight,
  containerStyle,
  updateScale,
  getScale,
  getDesignSize,
} = useScreenScale({
  designWidth: 8600,
  designHeight: 3600,
  debounceTime: 100,
  enableMapResize: true,
});
</script>
```

### 2. 地图组件适配

#### Cesium 地图适配

```vue
<script setup lang="ts">
import { useCesiumMapScale } from '/@/hooks/web/useScreenScale';

const cesiumViewer = ref(null);
const screenScale = useScreenScale();

// 地图就绪后注册缩放适配
const handleViewerReady = (viewer) => {
  cesiumViewer.value = viewer;
  
  // 注册 Cesium 地图缩放适配
  const cleanup = useCesiumMapScale(viewer, screenScale);
  
  // 组件卸载时清理
  onUnmounted(cleanup);
};
</script>
```

#### 通用地图适配

```vue
<script setup lang="ts">
import { useGenericMapScale } from '/@/hooks/web/useScreenScale';

const mapInstance = ref(null);
const screenScale = useScreenScale();

// 地图实例化后注册缩放适配
const initMap = () => {
  // 假设是高德地图
  mapInstance.value = new AMap.Map('container');
  
  // 注册通用地图缩放适配
  const cleanup = useGenericMapScale(
    mapInstance.value,
    'resize', // 地图的 resize 方法名
    screenScale
  );
  
  onUnmounted(cleanup);
};
</script>
```

### 3. 自定义缩放回调

```vue
<script setup lang="ts">
const screenScale = useScreenScale();

// 注册自定义缩放回调
const handleCustomResize = (scale) => {
  console.log('当前缩放比例:', scale);
  // 执行自定义逻辑
};

onMounted(() => {
  screenScale.onMapResize(handleCustomResize);
});

onUnmounted(() => {
  screenScale.offMapResize(handleCustomResize);
});
</script>
```

## API 参考

### useScreenScale 配置选项

```typescript
interface ScreenScaleOptions {
  designWidth?: number;      // 设计稿宽度，默认 8600
  designHeight?: number;     // 设计稿高度，默认 3600
  debounceTime?: number;     // 防抖时间，默认 100ms
  enableMapResize?: boolean; // 是否启用地图缩放，默认 true
}
```

### 返回值

```typescript
interface ScreenScaleReturn {
  scale: Readonly<Ref<number>>;                    // 当前缩放比例
  screenWidth: Readonly<Ref<number>>;              // 屏幕宽度
  screenHeight: Readonly<Ref<number>>;             // 屏幕高度
  containerStyle: ComputedRef<Record<string, string>>; // 容器样式
  updateScale: () => void;                         // 手动更新缩放
  getScale: () => number;                          // 获取当前缩放比例
  getDesignSize: () => { width: number; height: number }; // 获取设计稿尺寸
  onMapResize: (callback: (scale: number) => void) => void;  // 注册缩放回调
  offMapResize: (callback: (scale: number) => void) => void; // 移除缩放回调
}
```

## 样式说明

### 容器样式

组件会自动生成以下样式：

```css
.container {
  width: 8600px;
  height: 3600px;
  transform: scale(0.8); /* 根据屏幕大小计算 */
  transform-origin: center center;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -4300px; /* -width/2 */
  margin-top: -1800px;  /* -height/2 */
}
```

### 响应式断点

```less
// 1920px 以下
@media screen and (max-width: 1920px) {
  // 调整边框等细节
}

// 1366px 以下
@media screen and (max-width: 1366px) {
  // 调整布局比例
}
```

## 地图缩放优化

### Cesium 优化策略

1. **分辨率自适应**: 缩放比例 < 0.5 时降低渲染分辨率
2. **强制重新渲染**: 缩放后触发 `scene.requestRender()`
3. **画布尺寸更新**: 调用 `viewer.resize()`

### 性能考虑

- 使用防抖处理窗口 resize 事件
- 只在缩放比例真正变化时触发回调
- 支持渲染质量动态调整

## 注意事项

1. **CSS Transform**: 使用 CSS transform 进行缩放，不影响 DOM 结构
2. **事件坐标**: 缩放后需要注意鼠标事件坐标的转换
3. **字体渲染**: 启用了字体平滑以保证缩放后的清晰度
4. **地图性能**: 大幅缩放时会自动降低地图渲染质量

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 常见问题

### Q: 地图在缩放后显示异常？
A: 确保在地图初始化完成后注册缩放适配，并检查地图库的 resize 方法调用。

### Q: 缩放后文字模糊？
A: 已启用 `-webkit-font-smoothing: antialiased`，如仍有问题可调整 CSS 或使用 SVG 文字。

### Q: 如何禁用地图缩放适配？
A: 设置 `enableMapResize: false` 或不注册地图缩放回调。

### Q: 支持其他地图库吗？
A: 支持，使用 `useGenericMapScale` 并指定对应的 resize 方法名即可。
