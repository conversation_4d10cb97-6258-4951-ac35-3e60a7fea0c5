/*
 * @Description: 创建代理
 * @Author: XIAOLIJUN
 * @Date: 2022-07-07 19:41:05
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2022-07-11 19:44:33
 */
/**
 * Used to parse the .env.development proxy configuration
 */
import type { ProxyOptions } from 'vite';

type ProxyItem = [string, string];

type ProxyList = ProxyItem[];

type ProxyTargetList = Record<string, ProxyOptions>;

const httpsRE = /^https:\/\//;

/**
 * Generate proxy
 * @param list
 */
export function createProxy(list: ProxyList = [], env: ViteEnv) {
  const ret: ProxyTargetList = {};
  const { VITE_GLOB_API_URL } = env;
  for (const [prefix, target] of list) {
    const fullPrefix = `${VITE_GLOB_API_URL}${prefix}`;
    const isHttps = httpsRE.test(target);

    // https://github.com/http-party/node-http-proxy#options
    ret[fullPrefix] = {
      target: target,
      changeOrigin: true,
      ws: true,
      rewrite: (path) => path.replace(new RegExp(`^${fullPrefix}`), ''),
      // https is require secure=false
      ...(isHttps ? { secure: false } : {}),
    };
  }
  return ret;
}
