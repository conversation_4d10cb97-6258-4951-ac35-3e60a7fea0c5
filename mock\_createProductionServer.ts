/*
 * @Description: 创建生产环境Mock服务
 * @Author: XIAOLIJUN
 * @Date: 2022-07-07 19:41:05
 * @LastEditors: XIAOL<PERSON>JUN
 * @LastEditTime: 2022-07-10 23:23:02
 */
import { createProdMockServer } from 'vite-plugin-mock/es/createProdMockServer';

const modules = import.meta.globEager('./**/*.ts');

const mockModules: any[] = [];
Object.keys(modules).forEach((key) => {
  if (key.includes('/_')) {
    return;
  }
  mockModules.push(...modules[key].default);
});

/**
 * 用于生产环境。需要手动导入所有模块
 */
export function setupProdMockServer(VITE_GLOB_API_URL: string) {
  if (VITE_GLOB_API_URL) {
    mockModules.forEach((m) => {
      m.url = VITE_GLOB_API_URL + m.url;
    });
  }
  createProdMockServer(mockModules);
}
