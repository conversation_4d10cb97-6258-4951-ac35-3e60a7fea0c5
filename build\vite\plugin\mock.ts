/*
 * @Description: 用于开发和生产的模拟插件。https://github.com/anncwb/vite-plugin-mock
 * @Author: XIAOLIJUN
 * @Date: 2022-07-07 19:41:05
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2022-07-10 23:29:43
 */
import { viteMockServe } from 'vite-plugin-mock';

export function configMockPlugin(env: ViteEnv, isBuild: boolean) {
  const { VITE_GLOB_API_URL } = env;
  // 生产环境在Mock路径追加 VITE_GLOB_API_URL
  const BASE_URL = VITE_GLOB_API_URL && `'${VITE_GLOB_API_URL}'`;
  return viteMockServe({
    ignore: /^\_/,
    mockPath: 'mock',
    localEnabled: !isBuild,
    prodEnabled: isBuild,
    injectCode: `
      import { setupProdMockServer } from '../mock/_createProductionServer';

      setupProdMockServer(${BASE_URL});
      `,
  });
}
