/*
 * @Description: 示例树结构mock接口
 * @Author: XIAOLIJUN
 * @Date: 2022-07-07 19:41:02
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2023-03-27 15:23:20
 */
import { defHttp } from '/@/utils/http/axios';

enum Api {
  TREE_OPTIONS_LIST = '/mock/tree/getDemoOptions',
}

/**
 * @description: Get sample options value
 */
export const treeOptionsListApi = (params?: Recordable) =>
  defHttp.get<Recordable[]>({ url: Api.TREE_OPTIONS_LIST, params });
