/*
 * @Description: 统一权限认证接口
 * @Author: XIAOLIJUN
 * @Date: 2022-07-10 10:33:37
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2023-12-23 14:48:44
 */

/**
 * @description: 用户校验参数
 */
export interface AccessCodeParams {
  account: string;
  password: string | false;
}
/**
 * @description: 用户校验成功结果
 */
export interface AccessCodeResultModel {
  code: string;
  expire: number;
  time: string;
}

/**
 * @description: 登录接口参数
 */
export interface LoginByAppParams {
  clientId?: string;
  code?: string;
  osCategory?: string;
  qrType?: string;
  redirectUrl?: string;
}

export interface SingleLoginParams {
  clientId: string;
  account: string;
  password: string;
  captchaPointJson?: string;
  captchaSecretKey?: string;
  captchaToken?: string;
}

/**
 * @description: 菜单接口参数
 */
export interface MenuParams {
  appCode: string;
}

/**
 * @description: 功能按钮接口参数
 */
export interface PermParams {
  appCode: string;
}

/**
 * @description: 用户修改密码参数
 */
export interface UpdatePasswordParams {
  clientId: string;
  oldPassword: string;
  newPassword: string;
  repeatPassword: string;
}

/**
 * @description: 重置用户密码参数
 */
export interface ResetPasswordParams {
  clientId: string;
  loginAccount: string;
  oldRsaPassword: string;
  newRsaPassword: string;
}
