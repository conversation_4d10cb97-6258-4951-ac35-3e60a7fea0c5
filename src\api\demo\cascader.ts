/*
 * @Description: 区域级联mock接口
 * @Author: XIAOLIJUN
 * @Date: 2022-07-07 19:41:02
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2023-03-27 15:21:25
 */
import { defHttp } from '/@/utils/http/axios';
import { AreaModel, AreaParams } from '/@/api/demo/model/areaModel';

enum Api {
  AREA_RECORD = '/mock/cascader/getAreaRecord',
  AREA_ALL = '/mock/cascader/getAreaListAll',
}

export const areaRecord = (data: AreaParams) =>
  defHttp.post<AreaModel>({ url: Api.AREA_RECORD, data });

export const areaListAll = () => defHttp.post({ url: Api.AREA_ALL });
