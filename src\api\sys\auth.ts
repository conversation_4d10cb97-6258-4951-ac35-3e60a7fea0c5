/*
 * @Description: 统一权限用户认证接口
 * @Author: XIAOLIJUN
 * @Date: 2022-07-10 10:32:46
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2024-04-18 14:47:28
 */
import { defHttp } from '/@/utils/http/axios';
import {
  AccessCodeParams,
  AccessCodeResultModel,
  PermParams,
  LoginByAppParams,
  MenuParams,
  UpdatePasswordParams,
  ResetPasswordParams,
} from './model/authModel';

import { ErrorMessageMode } from '/#/axios';
import { buildUUID } from '/@/utils/uuid';

enum Api {
  // AUTH模式v1版本
  AccessCode = '/gateway/ctfo-platform-auth/auth/accessCode',
  LoginByApp = '/gateway/ctfo-platform-auth/sso/loginByApp',
  LogoutByApp = '/gateway/ctfo-platform-auth/sso/logoutByApp',
  GetUserInfo = '/gateway/ctfo-platform-system/system/getUserInfo',
  GetMenuList = '/gateway/ctfo-platform-system/func/getMenuList',
  GetPermList = '/gateway/ctfo-platform-system/func/queryFunctionListByUser',
  UpdatePassword = '/gateway/ctfo-platform-system/sysUser/updatePassword',
  ResetPassword = '/gateway/ctfo-platform-system/sysUser/updateUserPassword2',
  // AUTH模式v2版本
  GetPublicKey = '/gateway/ctfo-platform-auth/authClient/detail',
  SingleLogin = '/gateway/ctfo-platform-auth/sso/singleLogin',
  GetCaptcha = '/gateway/ctfo-platform-auth/captcha/get',
  CheckCaptcha = '/gateway/ctfo-platform-auth/captcha/check',

  // OUTER模式相关接口
  LoginByOuter = '/gateway/ctfo-outer-auth/api/appLogin',
  LogoutByOuter = '/gateway/ctfo-outer-auth/api/logout',
  GetOuterInfo = '/gateway/ctfo-outer-auth/api/getInfo',
}

/**
 * @description: 用户校验，获取accessCode
 */
export function accessCode(params: AccessCodeParams, mode: ErrorMessageMode = 'message') {
  return defHttp.post<AccessCodeResultModel>(
    { url: Api.AccessCode, params },
    { errorMessageMode: mode },
  );
}

/**
 * @description: v1版本SSO登录获取TOKEN
 */
export function loginByApp(params: LoginByAppParams, mode: ErrorMessageMode = 'message') {
  return defHttp.get({ url: Api.LoginByApp, params }, { errorMessageMode: mode });
}

/**
 * @description: 获取加密公钥
 */
export function getPublicKey(params: LoginByAppParams, mode: ErrorMessageMode = 'message') {
  return defHttp.get({ url: Api.GetPublicKey, params }, { errorMessageMode: mode });
}

/**
 * @description: v2版本登录获取TOKEN
 */
export function singleLogin(params: FormData, mode: ErrorMessageMode = 'message') {
  return defHttp.post(
    { url: Api.SingleLogin, params },
    { errorMessageMode: mode, isTransformResponse: false },
  );
}

/**
 * @description: v2版本获取图片验证码
 */
export function getCaptcha(captchaType, mode: ErrorMessageMode = 'message') {
  const params = { captchaType, clientUid: buildUUID(), ts: Date.now() };
  return defHttp.post(
    { url: Api.GetCaptcha, params },
    { errorMessageMode: mode, isTransformResponse: false },
  );
}

/**
 * @description: v2版本校验图片验证码
 */
export function checkCaptcha(params, mode: ErrorMessageMode = 'message') {
  return defHttp.post(
    { url: Api.CheckCaptcha, params },
    { errorMessageMode: mode, isTransformResponse: false },
  );
}

/**
 * @description: SSO登出
 */
export function logoutByApp(params: LoginByAppParams, mode: ErrorMessageMode = 'message') {
  return defHttp.get({ url: Api.LogoutByApp, params }, { errorMessageMode: mode });
}

/**
 * @description: 获取当前用户信息
 */
export function getAuthUserInfo(mode: ErrorMessageMode = 'message') {
  return defHttp.get({ url: Api.GetUserInfo }, { errorMessageMode: mode });
}

/**
 * @description: 获取当前菜单信息
 */
export function getAuthMenuList(params: MenuParams, mode: ErrorMessageMode = 'message') {
  return defHttp.get({ url: Api.GetMenuList, params }, { errorMessageMode: mode });
}

/**
 * @description: 获取当前用户按钮列表
 */
export function getPermList(params: PermParams, mode: ErrorMessageMode = 'message') {
  return defHttp.get({ url: Api.GetPermList, params }, { errorMessageMode: mode });
}

/**
 * @description: 登录OUTER获取TOKEN
 */
export function loginByOuter(
  params: { password: string; username: string },
  mode: ErrorMessageMode = 'message',
) {
  return defHttp.post(
    { url: Api.LoginByOuter, params },
    { errorMessageMode: mode, isTransformResponse: false },
  );
}

/**
 * @description: 获取OUTER当前用户信息
 */
export function getOuterInfo(mode: ErrorMessageMode = 'message') {
  return defHttp.get(
    { url: Api.GetOuterInfo },
    { errorMessageMode: mode, isTransformResponse: false },
  );
}

/**
 * @description: 退出OUTER当前用户
 */
export function logoutByOuter(mode: ErrorMessageMode = 'message') {
  return defHttp.get(
    { url: Api.LogoutByOuter },
    { errorMessageMode: mode, isTransformResponse: false },
  );
}

/**
 * @description: 当前用户修改密码
 */
export function updatePassword(params: UpdatePasswordParams, mode: ErrorMessageMode = 'message') {
  return defHttp.post({ url: Api.UpdatePassword, params }, { errorMessageMode: mode });
}

/**
 * @description: 非登录状态重置用户密码
 */
export function resetPassword(params: ResetPasswordParams, mode: ErrorMessageMode = 'message') {
  return defHttp.post({ url: Api.ResetPassword, params }, { errorMessageMode: mode });
}
